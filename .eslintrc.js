module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  extends: [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
  ],
  parser: "@typescript-eslint/parser",
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
      tsx: true,
    },
    ecmaVersion: "latest",
    sourceType: "module",
  },
  plugins: ["@typescript-eslint", "react", "react-hooks"],
  settings: {
    react: {
      version: "detect",
    },
  },
  rules: {
    "no-console": "off",
    "no-unused-vars": "off",
    "react/react-in-jsx-scope": "off",
    "prefer-const": "warn",
    "no-empty": "warn",
    "no-prototype-builtins": "warn",
    "no-async-promise-executor": "warn",
    "no-empty-pattern": "warn",
    "react/jsx-key": "warn",
    "no-constant-condition": "warn",
    "react/prop-types": "off",
    "react/display-name": "off",
    "no-extra-boolean-cast": "warn",
    "react/no-unescaped-entities": "warn",
    "react/jsx-no-target-blank": "warn",
    "no-var": "warn",
    "no-case-declarations": "warn",
    "no-misleading-character-class": "off",
    "react/no-unknown-property": "warn",
    "no-useless-catch": "off",
    "@typescript-eslint/no-empty-function": "warn",
    "@typescript-eslint/no-non-null-assertion": "warn",
    "@typescript-eslint/no-non-null-asserted-optional-chain": "warn",
    "@typescript-eslint/ban-types": "warn",
    "@typescript-eslint/no-inferrable-types": "warn",
    "@typescript-eslint/no-empty-interface": "warn",
    "@typescript-eslint/ban-ts-comment": "warn",
  },
  ignorePatterns: [
    "node_modules/",
    "build/",
    "dist/",
    "*.config.js",
    "*.config.mjs",
    "*.config.ts",
    "vite.config.*",
    "tailwind.config.*",
    "postcss.config.*",
    "playwright.config.*",
  ],
};
