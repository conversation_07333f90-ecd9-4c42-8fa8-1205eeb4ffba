// 测试名称生成逻辑
function testNameGeneration() {
  const existingNames = ["开始", "开始（1）", "开始（2）", "LLM", "LLM（1）"];
  
  const generateUniqueName = (baseName) => {
    // 提取基础名称（去掉可能存在的序号）
    const baseNameMatch = baseName.match(/^(.+?)(?:（(\d+)）)?$/);
    const cleanBaseName = baseNameMatch ? baseNameMatch[1] : baseName;

    // 查找已存在的最大序号
    let maxNumber = 0;
    existingNames.forEach((name) => {
      // 检查是否是同一个基础名称
      const match = name.match(
        new RegExp(
          `^${cleanBaseName.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")}(?:（(\\d+)）)?$`,
        ),
      );
      if (match) {
        // 如果匹配到了，提取序号
        const number = match[1] ? parseInt(match[1], 10) : 1;
        maxNumber = Math.max(maxNumber, number);
      }
    });

    return `${cleanBaseName}（${maxNumber + 1}）`;
  };

  console.log("现有名称:", existingNames);
  console.log("复制 '开始' ->", generateUniqueName("开始"));
  console.log("复制 '开始（1）' ->", generateUniqueName("开始（1）"));
  console.log("复制 '开始（2）' ->", generateUniqueName("开始（2）"));
  console.log("复制 'LLM' ->", generateUniqueName("LLM"));
  console.log("复制 'LLM（1）' ->", generateUniqueName("LLM（1）"));
  console.log("复制 '新节点' ->", generateUniqueName("新节点"));
}

testNameGeneration();
