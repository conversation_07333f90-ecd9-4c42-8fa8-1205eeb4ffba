# GitLab CI/CD 配置文件
before_script:
  - whoami
  - echo "Node.js version:" && node --version
  - echo "pnpm version:" && pnpm --version
  - pnpm i --frozen-lockfile

# 流水线阶段
stages:
  - check-types
  - check-format

# TypeScript 类型检查
check-types:
  stage: check-types
  script:
    - pnpm run check-types
  only:
    - merge_requests

# 代码格式检查
check-format:
  stage: check-format
  script:
    - pnpm run check-format
  only:
    - merge_requests

# TODO: 暂时不开启 eslint 阶段，项目中有许多原有的代码语法不符合规范，后续迭代中再排期陆续修复
# eslint:
#   stage: eslint
#   script:
#     - pnpm run lint
#   only:
#     - merge_requests

