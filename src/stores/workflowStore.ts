import { create } from "zustand";

import { BaseEdge, BuiltinNode, WorkflowData } from "@/types/workflow";
import { PanelState, WorkflowStore } from "@/types/zustand/workflow";
import { v4 as uuid } from "uuid";

export const useWorkflowStore = create<WorkflowStore>((set, get) => ({
  currentWorkflow: null,
  selectedNodeId: null,
  isDebugMode: false,
  isDirty: false,
  debugPanel: {
    isOpen: false,
    width: 360,
  },
  configPanel: {
    isOpen: false,
    width: 360,
  },

  setCurrentWorkflow: (workflow: WorkflowData) => {
    set({ currentWorkflow: workflow });
  },
  setSelectedNodeId: (id: string | null) => {
    set({ selectedNodeId: id });
    // 当选择节点时自动打开配置面板
    if (id) {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: true },
      }));
    } else {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: false },
      }));
    }
  },
  setIsDebugMode: (isDebugMode: boolean) => {
    set((state) => ({
      isDebugMode,
      debugPanel: { ...state.debugPanel, isOpen: isDebugMode },
    }));
  },
  setIsDirty: (isDirty: boolean) => {
    set({ isDirty });
  },
  setDebugPanel: (debugPanel: Partial<PanelState>) => {
    set((state) => ({
      debugPanel: {
        ...state.debugPanel,
        ...debugPanel,
        width:
          debugPanel.isOpen === false
            ? 360
            : debugPanel.width || state.debugPanel.width,
      },
    }));
  },
  setConfigPanel: (configPanel: Partial<PanelState>) => {
    set((state) => ({
      configPanel: {
        ...state.configPanel,
        ...configPanel,
        width:
          configPanel.isOpen === false
            ? 360
            : configPanel.width || state.configPanel.width,
      },
    }));
  },
  updateNode: (nodeId: string, updates: Partial<BuiltinNode>) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.map((node) =>
      node.id === nodeId ? { ...node, ...updates } : node,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  addNode: (node: Omit<BuiltinNode, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = [...currentWorkflow.nodes, { ...node, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  deleteNode: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.filter(
      (node) => node.id !== nodeId,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  addEdge: (edge: Omit<BaseEdge, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = [...currentWorkflow.edges, { ...edge, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  deleteEdge: (edgeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = currentWorkflow.edges.filter(
      (edge) => edge.id !== edgeId,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  getAvailableVariables: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return [];

    const node = currentWorkflow.nodes.find((node) => node.id === nodeId);
    if (!node) return [];

    const variables = [];

    return variables;
  },
}));
