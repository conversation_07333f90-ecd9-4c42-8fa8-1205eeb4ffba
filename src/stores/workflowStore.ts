import { create } from "zustand";

import { BaseEdge, BuiltinNode, WorkflowData } from "@/types/workflow";
import { PanelState, WorkflowStore } from "@/types/zustand/workflow";
import { v4 as uuid } from "uuid";

export const useWorkflowStore = create<WorkflowStore>((set, get) => ({
  currentWorkflow: null,
  selectedNodeId: null,
  isDebugMode: false,
  isDirty: false,
  debugPanel: {
    isOpen: false,
    width: 360,
  },
  configPanel: {
    isOpen: false,
    width: 360,
  },

  setCurrentWorkflow: (workflow: WorkflowData) => {
    set({ currentWorkflow: workflow });
  },
  setSelectedNodeId: (id: string | null) => {
    set({ selectedNodeId: id });
    // 当选择节点时自动打开配置面板
    if (id) {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: true },
      }));
    } else {
      set((state) => ({
        configPanel: { ...state.configPanel, isOpen: false },
      }));
    }
  },
  setIsDebugMode: (isDebugMode: boolean) => {
    set((state) => ({
      isDebugMode,
      debugPanel: { ...state.debugPanel, isOpen: isDebugMode },
    }));
  },
  setIsDirty: (isDirty: boolean) => {
    set({ isDirty });
  },
  setDebugPanel: (debugPanel: Partial<PanelState>) => {
    set((state) => ({
      debugPanel: {
        ...state.debugPanel,
        ...debugPanel,
        width:
          debugPanel.isOpen === false
            ? 360
            : debugPanel.width || state.debugPanel.width,
      },
    }));
  },
  setConfigPanel: (configPanel: Partial<PanelState>) => {
    set((state) => ({
      configPanel: {
        ...state.configPanel,
        ...configPanel,
        width:
          configPanel.isOpen === false
            ? 360
            : configPanel.width || state.configPanel.width,
      },
    }));
  },
  updateNode: (nodeId: string, updates: Partial<BuiltinNode>) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.map((node) =>
      node.id === nodeId ? { ...node, ...updates } : node,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  addNode: (node: Omit<BuiltinNode, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedNodes = [...currentWorkflow.nodes, { ...node, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });
  },
  deleteNode: (nodeId: string) => {
    const { currentWorkflow, selectedNodeId } = get();
    if (!currentWorkflow) return;

    const updatedNodes = currentWorkflow.nodes.filter(
      (node) => node.id !== nodeId,
    );

    // 删除与该节点相关的所有边
    const updatedEdges = currentWorkflow.edges.filter(
      (edge) => edge.source !== nodeId && edge.target !== nodeId,
    );

    // 如果被删除的节点当前被选中，关闭配置面板
    if (selectedNodeId === nodeId) {
      set({ selectedNodeId: null });
    }

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
        edges: updatedEdges,
      },
    });
  },
  copyNode: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const originalNode = currentWorkflow.nodes.find((node) => node.id === nodeId);
    if (!originalNode) return;

    // 生成唯一的节点名称
    const generateUniqueName = (baseName: string): string => {
      const existingNames = currentWorkflow.nodes.map((node) => node.label);

      // 检查基础名称是否已存在
      if (!existingNames.includes(baseName)) {
        return baseName;
      }

      // 提取基础名称（去掉可能存在的序号）
      const baseNameMatch = baseName.match(/^(.+?)(?:\（(\d+)\）)?$/);
      const cleanBaseName = baseNameMatch ? baseNameMatch[1] : baseName;

      // 查找已存在的最大序号
      let maxNumber = 0;
      existingNames.forEach((name) => {
        const match = name.match(new RegExp(`^${cleanBaseName.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(?:\（(\d+)\）)?$`));
        if (match) {
          const number = match[1] ? parseInt(match[1], 10) : 0;
          maxNumber = Math.max(maxNumber, number);
        }
      });

      return `${cleanBaseName}（${maxNumber + 1}）`;
    };

    // 创建复制的节点
    const copiedNode: BuiltinNode = {
      ...originalNode,
      id: uuid(),
      label: generateUniqueName(originalNode.label),
      position: {
        x: originalNode.position.x + 180, // 右侧偏移180px
        y: originalNode.position.y,
      },
      data: {
        ...originalNode.data,
        label: generateUniqueName(originalNode.label),
      },
    };

    const updatedNodes = [...currentWorkflow.nodes, copiedNode];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        nodes: updatedNodes,
      },
    });

    return copiedNode.id;
  },
  addEdge: (edge: Omit<BaseEdge, "id">) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = [...currentWorkflow.edges, { ...edge, id: uuid() }];

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  deleteEdge: (edgeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return;

    const updatedEdges = currentWorkflow.edges.filter(
      (edge) => edge.id !== edgeId,
    );

    set({
      currentWorkflow: {
        ...currentWorkflow,
        edges: updatedEdges,
      },
    });
  },
  getAvailableVariables: (nodeId: string) => {
    const { currentWorkflow } = get();
    if (!currentWorkflow) return [];

    const node = currentWorkflow.nodes.find((node) => node.id === nodeId);
    if (!node) return [];

    const variables = [];

    return variables;
  },
}));
