import {
  BaseEdge,
  BuiltinNode,
  Variable,
  WorkflowData,
} from "@/types/workflow";

export interface PanelState {
  isOpen: boolean;
  width: number;
}

export interface WorkflowStore {
  // 状态
  currentWorkflow: WorkflowData | null;
  selectedNodeId: string | null;
  isDebugMode: boolean;
  isDirty: boolean;
  debugPanel: PanelState;
  configPanel: PanelState;
  // ...其他状态

  // 操作方法
  setCurrentWorkflow: (workflow: WorkflowData) => void;
  setSelectedNodeId: (id: string | null) => void;
  setIsDebugMode: (isDebugMode: boolean) => void;
  setIsDirty: (isDirty: boolean) => void;
  // 面板操作方法
  setDebugPanel: (debugPanel: Partial<PanelState>) => void;
  setConfigPanel: (configPanel: Partial<PanelState>) => void;
  updateNode: (nodeId: string, updates: Partial<BuiltinNode>) => void;
  addNode: (node: Omit<BuiltinNode, "id">) => void;
  deleteNode: (nodeId: string) => void;
  copyNode: (nodeId: string) => string | undefined;
  addEdge: (edge: Omit<BaseEdge, "id">) => void;
  deleteEdge: (edgeId: string) => void;
  getAvailableVariables: (nodeId: string) => Variable[];
  // ...其他操作方法
}
