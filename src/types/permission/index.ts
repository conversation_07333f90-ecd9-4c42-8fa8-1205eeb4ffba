import { Member, PermissionScope, PermissionType } from "@/constants/enums";

export type SubjectType = "user" | "organization" | "tenant";

export interface ResourcePermission {
  permission_scope: PermissionScope;
  permissions: PermissionItem[];
}

export interface PermissionItem {
  subject_type: SubjectType;
  subject_id: string;
  permission_type: PermissionType;
  expire_at: string | null;
}

export interface ResourcePermissionItem {
  create_date: string;
  expire_at: string | null;
  granted_by: string;
  id: string;
  permission_type: PermissionType;
  subject_id: string;
  subject_name: string;
  subject_type: SubjectType;
}

export interface Resource {
  id: string;
  name: string;
  permission_scope: PermissionScope;
  owner_id: string;
  tenant_id: string;
  create_time: string;
  update_time: string;
}

export interface SelectedMember {
  id: string;
  name: string;
  type: Member;
  permission: PermissionType;
  avatar?: string;
  isReadonly?: boolean;
}
