import { Member } from "@/constants/enums";

export interface Organization {
  id: string;
  name: string;
  org_type: string;
  parent_id: string;
  children?: Organization[];
}

export interface OrganizationNode {
  id: string;
  name: string;
  children?: OrganizationNode[];
  isEditing?: boolean;
}

export interface OrganizationUserPageData {
  items: OrganizationUser[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface OrganizationUser {
  tenant_id: string;
  id: string;
  username: string;
  nickname: string;
  email: string;
  avatar: string | null;
  phone: string | null;
  is_active: boolean;
  is_superuser: boolean;
  status: string;
  create_at: string;
  updated_at: string;
  last_login_at: string | null;
  last_login_time: string | null;
  language: string;
  timezone: string;
  tenant_role: string;
  organizations: {
    organization_id: string;
    organization_name: string;
  }[];
}

export interface UserFormData {
  nickname: string;
  email: string;
  organization_id: string;
  role: string;
  phone?: string;
  password?: string;
}

export interface OrganizationMember {
  id: string;
  name: string;
  type: Member;
  has_children: boolean;
}
