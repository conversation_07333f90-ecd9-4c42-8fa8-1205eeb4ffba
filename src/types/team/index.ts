export interface TeamData {
  id: string;
  name: string;
  llm_id: string;
  embd_id: string;
  asr_id: string;
  img2txt_id: string;
  rerank_id: string;
  tts_id: string;
  parser_ids: string;
  credit: number;
  public_key: string;
  status: string;
  expire_time: string;
  max_user_count: number;
  tenant_picture: string;
  create_at: string;
  updated_at: string;
  user_count: number;
}

export interface TeamPageData {
  items: TeamData[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

export interface TeamFormData {
  name: string;
  max_user_count: number;
  expire_time: string;
  avatar?: string;
}
