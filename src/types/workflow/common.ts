import { WorkflowStatus } from "@/constants/enums";
import { BuiltinNode } from "./node";

export interface Variable {
  id: string;
  name: string;
  label: string;
  type: "string" | "number";
  description: string;
  maxLength: number;
  isRequired: boolean;
  nodeId: string;
}

export interface BaseEdge {
  id: string;
  source: string;
  target: string;
  sourceHandle: string;
  targetHandle: string;
}

export interface WorkflowData {
  id: string;
  label: string;
  description: string;
  nodes: BuiltinNode[];
  edges: BaseEdge[];
  status: WorkflowStatus;
  updateTime: string;
  publishTime: string;
}
