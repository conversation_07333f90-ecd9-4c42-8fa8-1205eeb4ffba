import { NodeType } from "@/constants/enums";
import { Variable } from "./common";

export interface BaseNodeData extends Record<string, unknown> {
  label: string;
  nodeType: NodeType;
  description?: string;
}

export interface StartNodeData extends BaseNodeData {
  variables?: Variable[];
}

export interface LLMNodeData extends BaseNodeData {
  model?: {
    provider: string;
    model_name: string;
    model_config: { temperature: number };
  };
  system_prompt?: string;
  user_prompt?: string;
}

export interface EndNodeData extends BaseNodeData {
  answer?: string;
}

export type NodeData = StartNodeData | LLMNodeData | EndNodeData;

export interface BuiltinNode {
  id: string;
  label: string;
  type: NodeType;
  position: { x: number; y: number };
  data: NodeData;
  description?: string;
}

/**
 * 节点选择面板中的节点项数据结构
 */
export interface NodeSelectItem {
  id: string;
  label: string;
  description: string;
  type: NodeType;
  category?: NodeCategory;
  enabled?: boolean;
}

/**
 * 节点分类枚举
 */
export enum NodeCategory {
  /** 无分类（直接展示） */
  NONE = "none",
  /** 逻辑判断 */
  LOGIC = "logic",
  /** 工具 */
  TOOLS = "tools",
}

/**
 * 节点分类配置
 */
export interface NodeCategoryConfig {
  key: NodeCategory;
  label: string;
  showTitle: boolean;
  nodes: NodeSelectItem[];
}

/**
 * 节点搜索过滤状态
 */
export interface SearchState {
  keyword: string;
  filteredNodes: NodeSelectItem[];
  hasResults: boolean;
}

/**
 * 节点添加位置信息
 */
export interface NodePosition {
  x: number;
  y: number;
}

export interface DragTransferData {
  nodeType: NodeType;
  nodeName: string;
  nodeData: NodeSelectItem;
}
