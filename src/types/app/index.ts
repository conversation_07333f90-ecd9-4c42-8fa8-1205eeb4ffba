import { Role } from "@/constants/enums";

export interface App extends AppConfig {
  resource_role: Role;
  tenant_user_role: Role;
  create_time: string;
  update_time: string;
  share_token: string;
}

export type CreateAppParams = Partial<AppConfig>;

export interface UpdateAppParams extends CreateAppParams {
  app_id: string;
  create_share_link?: boolean;
}

export interface Conversation {
  id: string;
  name: string;
  createdAt: string;
  lastMessage?: string;
}

export interface AppConfigFormData {
  name: string;
  description: string;
  id: string;
  icon: string;
  model: string;
  modelSettings: { temperature: number; max_tokens: number };
  prompt: string;
  openingStatement: string;
  speechToText: boolean;
  textToSpeech: boolean;
  datasets: {
    id: string;
    name: string;
  }[];
  memoryWindowSize: number;
}

export interface AppConfig {
  id: string;
  name: string;
  description: string;
  icon: string;
  model: {
    model_name: string;
    provider: string;
    model_settings: { temperature: number; max_tokens: number };
  };
  dataset: {
    datasets: {
      id: string;
      name: string;
    }[];
  };
  prompt: string;
  opening_statement: string;
  speech_to_text: boolean;
  text_to_speech: boolean;
  memory_window_size: number;
}

export interface ConversationDetail {
  created_at: string;
  id: string;
  name: string;
  updated_at: string;
  total_messages: number;
  messages: {
    category: string;
    text: string;
    id: string;
    conversation_id: string;
    sender: string;
    sender_name: string;
    timestamp: string;
  }[];
}

export interface ChatMessage {
  id: string;
  title: string;
  assistantId: string;
  answers: {
    id: string;
    content: string;
    assistantId: string;
  }[];
}
