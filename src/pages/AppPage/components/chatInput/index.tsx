import { Input as AntdInput } from "antd";
import { TextAreaProps } from "antd/lib/input/TextArea";
import { useEffect, useRef, useState } from "react";

import { Button } from "@/components/main/button";
import { PauseIcon, SendIcon, VoiceIcon } from "@/components/main/icon";
import { cn } from "@/utils/utils";
import VoiceRecording from "./components/VoiceRecording";
import { useAudioVisualization } from "./hooks/useAudioVisualization";
import { useSpeechRecognition } from "./hooks/useSpeechRecognition";

interface ChatInputProps
  extends Omit<TextAreaProps, "value" | "onChange" | "onSubmit"> {
  value?: string;
  button?: {
    disabled?: boolean;
  };
  enableVoiceInput?: boolean;
  onChange?: (str?: string) => void;
  onPressShiftEnter?: TextAreaProps["onPressEnter"];
  onSubmit?: (str?: string) => void;
  generating?: boolean;
  onStop?: () => void;
}

export default function ChatInput({
  onChange,
  onPressEnter,
  onPressShiftEnter,
  onSubmit,
  button,
  enableVoiceInput,
  className,
  value,
  generating,
  onStop,
  ...rest
}: ChatInputProps) {
  const [isVoiceMode, setIsVoiceMode] = useState(false);
  const [recordingTime, setRecordingTime] = useState(0);
  const initValueRef = useRef(value);

  // 语音识别 Hook
  const {
    isListening,
    transcript,
    error: speechError,
    isSupported: speechSupported,
    startListening,
    stopListening,
    resetTranscript,
  } = useSpeechRecognition({
    language: "zh-CN",
    continuous: true,
    interimResults: true,
  });

  // 音频可视化 Hook
  const {
    barHeights,
    isRecording,
    startRecording,
    stopRecording,
    error: audioError,
    isSupported: audioSupported,
  } = useAudioVisualization({
    barCount: 240,
    minBarHeight: 6,
    maxBarHeight: 20,
    updateInterval: 100, // 减慢推进速度
  });

  const handleChange: TextAreaProps["onChange"] = (e) => {
    onChange?.(e.target.value);
  };

  // 处理语音识别结果
  useEffect(() => {
    if (isVoiceMode) {
      if (transcript) {
        onChange?.(`${initValueRef.current || ""}${transcript}`);
      }
    } else {
      initValueRef.current = value;
    }
  }, [transcript, isVoiceMode, value, onChange]);

  // 录音时间计时器
  useEffect(() => {
    let interval: NodeJS.Timeout;

    if (isVoiceMode && (isListening || isRecording)) {
      interval = setInterval(() => {
        setRecordingTime((prev) => prev + 1);
      }, 1000);
    } else {
      setRecordingTime(0);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isVoiceMode, isListening, isRecording]);

  // 开始语音输入
  const handleStartVoiceInput = async () => {
    if (!speechSupported || !audioSupported) {
      console.error("浏览器不支持语音识别或音频录制功能");
      return;
    }

    setIsVoiceMode(true);
    resetTranscript();

    try {
      await startRecording();
      startListening();
    } catch (error) {
      console.error("启动语音输入失败:", error);
      setIsVoiceMode(false);
    }
  };

  // 完成语音输入
  const handleCompleteVoiceInput = () => {
    setIsVoiceMode(false);
    stopListening();
    stopRecording();
    setRecordingTime(0);
  };

  // 检查是否有错误
  const hasError = speechError || audioError;

  return (
    <div className="relative flex w-full flex-col gap-1 rounded-xl border border-solid border-border-1 bg-white p-3">
      <AntdInput.TextArea
        className={cn(
          "w-full rounded-none border-none p-0 text-sm leading-5 focus:shadow-none",
          className,
        )}
        {...rest}
        value={value}
        onChange={handleChange}
        onPressEnter={(e) => {
          e.persist();
          e.preventDefault();
          if (e.shiftKey) {
            onChange?.(value + "\n");
            onPressShiftEnter?.(e);
          } else {
            onPressEnter?.(e);
          }
        }}
        autoSize={{
          minRows: 1,
          maxRows: 7,
        }}
      />
      {isVoiceMode ? (
        <VoiceRecording
          barHeights={barHeights}
          recordingTime={recordingTime}
          onComplete={handleCompleteVoiceInput}
        />
      ) : (
        <div className="flex justify-end gap-1 text-base text-text-2">
          {enableVoiceInput ? (
            <Button
              variant="outline"
              size="iconSm"
              onClick={handleStartVoiceInput}
              disabled={isVoiceMode || !speechSupported || !audioSupported}
              className={cn(
                isVoiceMode &&
                  "border-primary-default bg-primary-default text-white",
              )}
            >
              <VoiceIcon />
            </Button>
          ) : null}
          {generating ? (
            <Button variant="outline" size="iconSm" onClick={() => onStop?.()}>
              <PauseIcon />
            </Button>
          ) : (
            <Button
              variant="outline"
              size="iconSm"
              disabled={button?.disabled}
              onClick={() => !button?.disabled && onSubmit?.(value)}
            >
              <SendIcon />
            </Button>
          )}
        </div>
      )}

      {/* 错误提示 */}
      {hasError && (
        <div className="mt-1 text-sm text-red-1">
          {speechError || audioError}
        </div>
      )}
    </div>
  );
}
