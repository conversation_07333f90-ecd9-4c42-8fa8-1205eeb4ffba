import { useCallback, useEffect, useRef, useState } from "react";

interface AudioVisualizationOptions {
  barCount?: number;
  minBarHeight?: number;
  maxBarHeight?: number;
  smoothingTimeConstant?: number;
  fftSize?: number;
  updateInterval?: number;
}

interface UseAudioVisualizationReturn {
  barHeights: number[];
  isRecording: boolean;
  startRecording: () => Promise<void>;
  stopRecording: () => void;
  error: string | null;
  isSupported: boolean;
  volume: number;
}

export const useAudioVisualization = (
  options: AudioVisualizationOptions = {},
): UseAudioVisualizationReturn => {
  const {
    barCount = 150, // 增加条形数量以获得更细腻的波形
    minBarHeight = 6,
    maxBarHeight = 20,
    smoothingTimeConstant = 0.6, // 降低平滑度以获得更快的响应
    fftSize = 512, // 增加FFT大小以获得更好的频率分辨率
    updateInterval = 16, // 约60fps的更新频率
  } = options;

  const [barHeights, setBarHeights] = useState<number[]>(
    Array(barCount).fill(minBarHeight),
  );
  const [isRecording, setIsRecording] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [volume, setVolume] = useState(0);

  const audioContextRef = useRef<AudioContext | null>(null);
  const analyserRef = useRef<AnalyserNode | null>(null);
  const microphoneRef = useRef<MediaStreamAudioSourceNode | null>(null);
  const streamRef = useRef<MediaStream | null>(null);
  const animationFrameRef = useRef<number | null>(null);
  const dataArrayRef = useRef<Uint8Array<ArrayBuffer> | null>(null);
  const lastUpdateRef = useRef<number>(0);

  // 检查浏览器是否支持音频录制
  const isSupported = !!(
    typeof window !== "undefined" &&
    navigator.mediaDevices &&
    typeof navigator.mediaDevices.getUserMedia === "function" &&
    (window.AudioContext || (window as any).webkitAudioContext)
  );

  // 初始化音频上下文
  const initializeAudioContext = useCallback(async () => {
    try {
      const AudioContext =
        window.AudioContext || (window as any).webkitAudioContext;
      audioContextRef.current = new AudioContext();

      if (audioContextRef.current.state === "suspended") {
        await audioContextRef.current.resume();
      }

      return audioContextRef.current;
    } catch (err) {
      console.error("Failed to initialize audio context:", err);
      setError("音频初始化失败");
      return null;
    }
  }, []);

  // 获取麦克风权限并创建音频分析器
  const setupAudioAnalyser = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100, // 设置采样率
        },
      });

      streamRef.current = stream;

      const audioContext = await initializeAudioContext();
      if (!audioContext) return false;

      // 创建音频分析器
      analyserRef.current = audioContext.createAnalyser();
      analyserRef.current.fftSize = fftSize;
      analyserRef.current.smoothingTimeConstant = smoothingTimeConstant;
      analyserRef.current.minDecibels = -90;
      analyserRef.current.maxDecibels = -10;

      // 创建麦克风音频源
      microphoneRef.current = audioContext.createMediaStreamSource(stream);
      microphoneRef.current.connect(analyserRef.current);

      // 初始化数据数组
      const bufferLength = analyserRef.current.frequencyBinCount;
      dataArrayRef.current = new Uint8Array(bufferLength);

      return true;
    } catch (err) {
      console.error("Failed to setup audio analyser:", err);
      setError("无法访问麦克风，请检查权限设置");
      return false;
    }
  }, [initializeAudioContext, fftSize, smoothingTimeConstant]);

  // 分析音频数据并更新波形图
  const analyzeAudio = useCallback(
    (timestamp: number) => {
      if (!analyserRef.current || !dataArrayRef.current || !isRecording) return;

      // 控制更新频率
      if (timestamp - lastUpdateRef.current < updateInterval) {
        animationFrameRef.current = requestAnimationFrame(analyzeAudio);
        return;
      }
      lastUpdateRef.current = timestamp;

      analyserRef.current.getByteFrequencyData(dataArrayRef.current);

      // 计算音量
      const sum = dataArrayRef.current.reduce((acc, value) => acc + value, 0);
      const average = sum / dataArrayRef.current.length;
      const normalizedVolume = average / 255;
      setVolume(normalizedVolume);

      // 计算最新一帧的barHeight
      const segmentSize = Math.floor(dataArrayRef.current.length / barCount);
      const newBars: number[] = [];
      for (let i = 0; i < barCount; i++) {
        const start = i * segmentSize;
        const end = start + segmentSize;
        let sumSquares = 0;
        let count = 0;
        for (let j = start; j < end && j < dataArrayRef.current.length; j++) {
          const value = dataArrayRef.current[j] / 255;
          sumSquares += value * value;
          count++;
        }
        const rms = count > 0 ? Math.sqrt(sumSquares / count) : 0;
        const logValue = rms > 0 ? Math.log10(rms * 9 + 1) / Math.log10(10) : 0;
        const compressedValue = Math.pow(logValue, 0.8);
        const barHeight =
          minBarHeight + compressedValue * (maxBarHeight - minBarHeight);
        const randomFactor = 0.95 + Math.random() * 0.1;
        newBars.push(
          Math.max(
            minBarHeight,
            Math.min(maxBarHeight, barHeight * randomFactor),
          ),
        );
      }

      // 滑动窗口推进：每次推进一帧
      setBarHeights((prev) => {
        // 只保留barCount-1个旧的，加上新一帧
        const next = prev.slice(1).concat(newBars[newBars.length - 1]);
        return next;
      });

      // 继续下一帧分析
      if (isRecording) {
        animationFrameRef.current = requestAnimationFrame(analyzeAudio);
      }
    },
    [barCount, minBarHeight, maxBarHeight, isRecording, updateInterval],
  );

  // 开始录制
  const startRecording = useCallback(async () => {
    if (!isSupported) {
      setError("当前浏览器不支持音频录制功能");
      return;
    }

    if (isRecording) return;

    setError(null);
    lastUpdateRef.current = 0;

    const success = await setupAudioAnalyser();
    if (success) {
      setIsRecording(true);
      animationFrameRef.current = requestAnimationFrame(analyzeAudio);
    }
  }, [isSupported, isRecording, setupAudioAnalyser, analyzeAudio]);

  // 停止录制
  const stopRecording = useCallback(() => {
    setIsRecording(false);

    // 停止动画帧
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // 停止音频流
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }

    // 断开音频连接
    if (microphoneRef.current) {
      microphoneRef.current.disconnect();
      microphoneRef.current = null;
    }

    // 关闭音频上下文
    if (audioContextRef.current && audioContextRef.current.state !== "closed") {
      audioContextRef.current.close();
      audioContextRef.current = null;
    }

    // 重置波形图
    setBarHeights(Array(barCount).fill(minBarHeight));
    setVolume(0);
    lastUpdateRef.current = 0;
  }, [barCount, minBarHeight]);

  // 清理资源
  useEffect(() => {
    return () => {
      stopRecording();
    };
  }, [stopRecording]);

  return {
    barHeights,
    isRecording,
    startRecording,
    stopRecording,
    error,
    isSupported,
    volume,
  };
};
