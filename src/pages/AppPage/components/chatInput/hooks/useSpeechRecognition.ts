import { useCallback, useEffect, useRef, useState } from "react";

// Web Speech API 类型声明补充
interface SpeechRecognition extends EventTarget {
  lang: string;
  continuous: boolean;
  interimResults: boolean;
  maxAlternatives: number;
  start(): void;
  stop(): void;
  abort(): void;
  onaudioend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onaudiostart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onerror:
    | ((this: SpeechRecognition, ev: SpeechRecognitionErrorEvent) => any)
    | null;
  onnomatch:
    | ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any)
    | null;
  onresult:
    | ((this: SpeechRecognition, ev: SpeechRecognitionEvent) => any)
    | null;
  onsoundend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onsoundstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechend: ((this: SpeechRecognition, ev: Event) => any) | null;
  onspeechstart: ((this: SpeechRecognition, ev: Event) => any) | null;
  onstart: ((this: SpeechRecognition, ev: Event) => any) | null;
}

interface SpeechRecognitionEvent extends Event {
  readonly resultIndex: number;
  readonly results: SpeechRecognitionResultList;
}

interface SpeechRecognitionResultList {
  [index: number]: SpeechRecognitionResult;
  length: number;
  item(index: number): SpeechRecognitionResult;
}

interface SpeechRecognitionResult {
  [index: number]: SpeechRecognitionAlternative;
  length: number;
  isFinal: boolean;
  item(index: number): SpeechRecognitionAlternative;
}

interface SpeechRecognitionAlternative {
  transcript: string;
  confidence: number;
}

interface SpeechRecognitionErrorEvent extends Event {
  error: string;
  message: string;
}

interface SpeechRecognitionOptions {
  language?: string;
  continuous?: boolean;
  interimResults?: boolean;
  maxAlternatives?: number;
}

interface UseSpeechRecognitionReturn {
  isListening: boolean;
  transcript: string;
  interimTranscript: string;
  finalTranscript: string;
  error: string | null;
  isSupported: boolean;
  startListening: () => void;
  stopListening: () => void;
  resetTranscript: () => void;
}

// 扩展 Window 接口以支持 webkitSpeechRecognition
declare global {
  interface Window {
    SpeechRecognition: {
      new (): SpeechRecognition;
    };
    webkitSpeechRecognition: {
      new (): SpeechRecognition;
    };
  }
}

export const useSpeechRecognition = (
  options: SpeechRecognitionOptions = {},
): UseSpeechRecognitionReturn => {
  const {
    language = "zh-CN",
    continuous = true,
    interimResults = true,
    maxAlternatives = 1,
  } = options;

  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState("");
  const [interimTranscript, setInterimTranscript] = useState("");
  const [finalTranscript, setFinalTranscript] = useState("");
  const [error, setError] = useState<string | null>(null);

  const recognitionRef = useRef<SpeechRecognition | null>(null);
  const isListeningRef = useRef(false);

  // 检查浏览器是否支持语音识别
  const isSupported = !!(
    typeof window !== "undefined" &&
    (window.SpeechRecognition || window.webkitSpeechRecognition)
  );

  // 初始化语音识别
  const initializeRecognition = useCallback(() => {
    if (!isSupported) return null;

    const SpeechRecognition =
      window.SpeechRecognition || window.webkitSpeechRecognition;
    const recognition = new SpeechRecognition();

    recognition.lang = language;
    recognition.continuous = continuous;
    recognition.interimResults = interimResults;
    recognition.maxAlternatives = maxAlternatives;

    // 处理识别结果
    recognition.onresult = (event: SpeechRecognitionEvent) => {
      let interimTranscriptValue = "";
      let finalTranscriptValue = "";

      for (let i = event.resultIndex; i < event.results.length; i++) {
        const result = event.results[i];
        const transcriptValue = result[0].transcript;

        if (result.isFinal) {
          finalTranscriptValue += transcriptValue;
        } else {
          interimTranscriptValue += transcriptValue;
        }
      }

      setInterimTranscript(interimTranscriptValue);

      if (finalTranscriptValue) {
        setFinalTranscript((prev) => prev + finalTranscriptValue);
        setTranscript((prev) => prev + finalTranscriptValue);
      }

      // 更新完整的转录文本（包括临时结果）
      const fullTranscript =
        finalTranscript + finalTranscriptValue + interimTranscriptValue;
      setTranscript(fullTranscript);
    };

    // 处理错误
    recognition.onerror = (event: SpeechRecognitionErrorEvent) => {
      console.error("Speech recognition error:", event.error);
      setError(`语音识别错误: ${event.error}`);
      setIsListening(false);
      isListeningRef.current = false;
    };

    // 处理识别开始
    recognition.onstart = () => {
      setIsListening(true);
      isListeningRef.current = true;
      setError(null);
    };

    // 处理识别结束
    recognition.onend = () => {
      setIsListening(false);
      isListeningRef.current = false;

      // 如果是意外结束且用户还想继续监听，则重新开始
      if (isListeningRef.current && continuous) {
        try {
          recognition.start();
        } catch (err) {
          console.error("Failed to restart recognition:", err);
        }
      }
    };

    return recognition;
  }, [
    language,
    continuous,
    interimResults,
    maxAlternatives,
    finalTranscript,
    isSupported,
  ]);

  // 开始监听
  const startListening = useCallback(() => {
    if (!isSupported) {
      setError("当前浏览器不支持语音识别功能");
      return;
    }

    if (isListeningRef.current) return;

    try {
      if (!recognitionRef.current) {
        recognitionRef.current = initializeRecognition();
      }

      if (recognitionRef.current) {
        recognitionRef.current.start();
        isListeningRef.current = true;
      }
    } catch (err) {
      console.error("Failed to start speech recognition:", err);
      setError("启动语音识别失败");
    }
  }, [isSupported, initializeRecognition]);

  // 停止监听
  const stopListening = useCallback(() => {
    if (recognitionRef.current && isListeningRef.current) {
      isListeningRef.current = false;
      recognitionRef.current.stop();
    }
  }, []);

  // 重置转录文本
  const resetTranscript = useCallback(() => {
    setTranscript("");
    setInterimTranscript("");
    setFinalTranscript("");
    setError(null);
  }, []);

  // 清理资源
  useEffect(() => {
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
        recognitionRef.current = null;
      }
    };
  }, []);

  return {
    isListening,
    transcript,
    interimTranscript,
    finalTranscript,
    error,
    isSupported,
    startListening,
    stopListening,
    resetTranscript,
  };
};
