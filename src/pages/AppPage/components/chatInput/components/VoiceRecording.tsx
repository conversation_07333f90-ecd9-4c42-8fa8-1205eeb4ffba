import React from "react";

import { Button } from "@/components/main/button";
import { cn } from "@/utils/utils";

interface VoiceRecordingProps {
  barHeights: number[];
  recordingTime: number;
  onComplete: () => void;
  className?: string;
}

// 格式化录音时间
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, "0")}:${secs.toString().padStart(2, "0")}`;
};

// 单个波形条组件
const WaveformBar: React.FC<{
  height: number;
  index: number;
  isActive: boolean;
}> = ({ height, index, isActive }) => {
  return (
    <div
      className={cn(
        "rounded-full bg-text-4 transition-all duration-75 ease-out",
        isActive && "animate-pulse",
      )}
      style={{
        width: "1px",
        height: `${Math.max(6, height)}px`,
        animationDelay: `${index * 20}ms`,
        opacity: isActive ? 1 : 0.6,
      }}
    />
  );
};

// 波形图组件
const Waveform: React.FC<{
  barHeights: number[];
  isRecording: boolean;
}> = ({ barHeights, isRecording }) => {
  // 直接渲染barHeights，最新的在最右侧
  const bars = [...barHeights].reverse();
  return (
    <div className="flex h-8 flex-1 items-center justify-end overflow-hidden">
      <div className="flex items-center justify-center gap-0.5">
        {bars.map((height, index) => (
          <WaveformBar
            key={index}
            height={height}
            index={index}
            isActive={isRecording}
          />
        ))}
      </div>
    </div>
  );
};

// 录音时间显示组件
const RecordingTimer: React.FC<{ time: number }> = ({ time }) => {
  return (
    <span className="font-mono text-sm tabular-nums text-text-2">
      {formatTime(time)}
    </span>
  );
};

// 完成按钮组件
const CompleteButton: React.FC<{ onClick: () => void }> = ({ onClick }) => {
  return (
    <Button
      variant="outline"
      size="iconSm"
      onClick={onClick}
      className="border-border-1 text-text-2 transition-colors hover:border-text-2 hover:text-text-1"
    >
      <svg
        width="1em"
        height="1em"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.3332 4L5.99984 11.3333L2.6665 8"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </Button>
  );
};

// 主录音组件
export const VoiceRecording: React.FC<VoiceRecordingProps> = ({
  barHeights,
  recordingTime,
  onComplete,
  className,
}) => {
  return (
    <div
      className={cn(
        "flex w-full items-center px-0 py-2",
        "duration-300 animate-in slide-in-from-bottom-2",
        className,
      )}
    >
      {/* 波形图区域 */}
      <Waveform barHeights={barHeights} isRecording={true} />

      {/* 右侧控制区域 */}
      <div className="ml-3 flex items-center gap-2">
        <RecordingTimer time={recordingTime} />
        <CompleteButton onClick={onComplete} />
      </div>
    </div>
  );
};

export default VoiceRecording;
