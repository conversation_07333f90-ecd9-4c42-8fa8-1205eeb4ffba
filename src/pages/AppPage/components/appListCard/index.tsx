import { Dropdown } from "antd";

import { Button } from "@/components/main/button";
import {
  ClockIcon,
  EditIcon,
  EllipsisIcon,
  TriangleIcon,
} from "@/components/main/icon";
import ListCard from "@/components/main/listCard";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import { App } from "@/types/app";
import { isResourceAdmin } from "@/utils/permission";
import { getColorFromString } from "@/utils/styleUtils";
import { convertUTCToLocalTimezone, getAppChatLink } from "@/utils/utils";

type AppListCardProps = {
  data: App;
  onDetele: (id: string) => void;
  onEdit: (id: string) => void;
};

export default function AppListCard({
  data,
  onDetele,
  onEdit,
}: AppListCardProps) {
  const navigate = useCustomNavigate();
  const chatLink = getAppChatLink(data.id, data.share_token);

  const color = getColorFromString(data.id);
  const editable = isResourceAdmin(data.resource_role, data.tenant_user_role);

  const handleClick = async () => {
    navigate(`/app/${data.id}/config`);
  };

  const handleRun = () => {
    window.open(chatLink);
  };

  const renderIcon = () => {
    return <span className="text-xl">✨</span>;
  };

  const renderFooter = () => {
    return (
      <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
        <Button
          variant="outline"
          className="w-full !text-primary-default"
          size="sm"
          onClick={() => {
            onEdit(data.id);
          }}
        >
          <EditIcon className="text-base" />
          编辑应用
        </Button>
        <Dropdown
          menu={{
            items: [{ key: "delete", label: "删除" }],
            onClick: () => onDetele(data.id),
          }}
          getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
        >
          <Button variant="outline" size="iconSm">
            <EllipsisIcon className="text-base text-text-2" />
          </Button>
        </Dropdown>
      </div>
    );
  };

  const renderDescription = () => {
    return (
      <>
        <ClockIcon className="mr-1 text-base text-text-4" />
        <div className="line-clamp-1 break-all">
          {convertUTCToLocalTimezone(data.update_time)}
        </div>
      </>
    );
  };

  const renderTopRight = () => {
    return (
      <Button
        variant="outline"
        size="xs"
        onClick={(e) => {
          e.stopPropagation();
          handleRun();
        }}
      >
        <TriangleIcon className="text-sm text-text-1" />
        运行
      </Button>
    );
  };

  return (
    <ListCard
      title={data.name}
      content={data.description}
      iconWrapperClass={color}
      onClick={handleClick}
      renderIcon={renderIcon}
      renderFooter={editable ? renderFooter : null}
      renderDescription={renderDescription}
      renderTopRight={renderTopRight}
    />
  );
}
