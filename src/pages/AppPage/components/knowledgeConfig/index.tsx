import { Button, Collapse, Form, Select, Space, Switch } from "antd";
import { useState } from "react";

import SliderWithInput from "@/components/antd/sliderWithInput";
import {
  CollapseIcon,
  DeleteIcon,
  ExpandIcon,
  KnowledgeIcon,
} from "@/components/main/icon";

import "./index.less";

const { Panel } = Collapse;

interface IKnowledgeCardProps {
  fieldName: number;
  knowledgeName: string;
  parentFieldName: string;
  onRemove: () => void;
}

export default function KnowledgeConfig({
  fieldName,
  knowledgeName,
  parentFieldName,
  onRemove,
}: IKnowledgeCardProps) {
  const [expanded, setExpanded] = useState(false);
  const useRerank = Form.useWatch(
    [parentFieldName, fieldName, "useRerank"],
    Form.useFormInstance(),
  );

  return (
    <Collapse
      className="knowledge-config-collapse mb-3 bg-white"
      size="small"
      onChange={(keys) => setExpanded(keys.length > 0)}
      collapsible="icon"
    >
      <Panel
        key={fieldName}
        showArrow={false}
        header={
          <div className="flex w-full items-center justify-between">
            <Space>
              <KnowledgeIcon className="text-base text-text-2" />
              <span className="text-xs">{knowledgeName}</span>
            </Space>
            <Space>
              {/* 暂时关闭展开功能，后续再支持 */}
              {/* {expanded ? (
                <Button
                  type="text"
                  size="small"
                  icon={<CollapseIcon className="text-base text-text-3" />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setExpanded(false);
                  }}
                />
              ) : (
                <Button
                  type="text"
                  size="small"
                  icon={<ExpandIcon className="text-base text-text-3" />}
                  onClick={(e) => {
                    e.stopPropagation();
                    setExpanded(true);
                  }}
                />
              )} */}
              <Button
                type="text"
                size="small"
                icon={<DeleteIcon className="text-base text-text-3" />}
                onClick={(e) => {
                  e.stopPropagation();
                  onRemove();
                }}
              />
            </Space>
          </div>
        }
      >
        <div className="flex flex-col gap-3">
          <Form.Item name={[fieldName, "similarityThreshold"]} noStyle>
            <SliderWithInput
              label="相似度阈值"
              size="small"
              min={0}
              max={1}
              step={0.1}
            />
          </Form.Item>
          <Form.Item name={[fieldName, "vectorSimilarityWeight"]} noStyle>
            <SliderWithInput
              min={0}
              max={1}
              step={0.1}
              label="向量相似度权重"
              size="small"
            />
          </Form.Item>
          <Form.Item name={[fieldName, "topN"]} noStyle>
            <SliderWithInput
              min={1}
              max={50}
              label="返回文档块数量(top_n)"
              size="small"
            />
          </Form.Item>
          <Form.Item name={[fieldName, "topK"]} noStyle>
            <SliderWithInput
              min={1}
              max={100}
              label="候选文档块数量(top_k)"
              size="small"
            />
          </Form.Item>
          <div>
            <Form.Item
              name={[fieldName, "useRerank"]}
              label="使用 Rerank 模型"
              className="use-rerank-form-item mb-1"
              valuePropName="checked"
              layout="horizontal"
              labelCol={{ span: 21 }}
              wrapperCol={{ span: 3 }}
              labelAlign="left"
              colon={false}
            >
              <Switch size="small" />
            </Form.Item>
            {useRerank ? (
              <Form.Item name={[fieldName, "rerankModel"]} noStyle>
                <Select
                  className="w-full"
                  options={[
                    {
                      value: "BGE Rerank v2-M3",
                      label: "BGE Rerank v2-M3",
                    },
                  ]}
                />
              </Form.Item>
            ) : null}
          </div>
        </div>
      </Panel>
    </Collapse>
  );
}
