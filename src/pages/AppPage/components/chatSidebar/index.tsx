import { ChatAddIcon } from "@/components/main/icon";
import type { Conversation } from "@/types/app";
import { cn } from "@/utils/utils";
import { Button } from "antd";
import ConversationItem from "../conversationItem";

interface ChatSidebarProps {
  conversations: Conversation[];
  selectedConversationId: string | undefined;
  visible: boolean;
  appColor: string;
  appName: string;
  onSelectConversation: (id: string) => void;
  onDeleteConversation: (id: string) => void;
  onNewConversation: () => void;
}

export default function ChatSidebar({
  visible,
  conversations,
  selectedConversationId,
  appColor,
  appName,
  onSelectConversation,
  onDeleteConversation,
  onNewConversation,
}: ChatSidebarProps) {
  return (
    <div
      className={cn(
        "flex w-60 flex-col border-r border-border-1 bg-bg-light-3 p-6 transition-transform duration-300 ease-in-out",
        "md:relative md:translate-x-0",
        visible
          ? "absolute inset-y-0 left-0 z-50 translate-x-0 shadow-lg md:shadow-none"
          : "absolute inset-y-0 left-0 z-50 -translate-x-full",
      )}
    >
      {/* 应用头部 */}
      <div className="border-b border-border-1 pb-4">
        <div className="mb-4 flex items-center gap-2">
          <div
            className={cn(
              "flex h-8 w-8 items-center justify-center rounded-lg text-2xl",
              appColor,
            )}
          >
            ✨
          </div>
          <div className="flex-1">
            <h1 className="text-sm font-medium">{appName}</h1>
          </div>
        </div>
        <Button onClick={onNewConversation} className="w-full">
          <ChatAddIcon className="text-base" />
          开启新对话
        </Button>
      </div>
      <div className="flex flex-1 flex-col gap-1 overflow-y-auto pt-4">
        {conversations.map((conversation) => (
          <ConversationItem
            key={conversation.id}
            conversation={conversation}
            isSelected={selectedConversationId === conversation.id}
            onSelect={onSelectConversation}
            onDelete={onDeleteConversation}
          />
        ))}
      </div>
    </div>
  );
}
