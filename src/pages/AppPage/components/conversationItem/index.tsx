import { Button, Dropdown } from "antd";

import { MoreIcon } from "@/components/main/icon";
import type { Conversation } from "@/types/app";
import { cn } from "@/utils/utils";

interface ConversationItemProps {
  conversation: Conversation;
  isSelected: boolean;
  onSelect: (id: string) => void;
  onDelete: (id: string) => void;
}

export default function ConversationItem({
  conversation,
  isSelected,
  onSelect,
  onDelete,
}: ConversationItemProps) {
  return (
    <div
      className={cn(
        "group relative cursor-pointer rounded-lg px-4 py-1 text-sm transition-all duration-200",
        isSelected ? "bg-bg-primary-1" : "hover:bg-bg-light-4",
      )}
      onClick={() => onSelect(conversation.id)}
    >
      <div className="flex items-center justify-between gap-2">
        <div
          className={cn(
            "min-w-0 flex-1 truncate",
            isSelected ? "text-primary-default" : "",
          )}
        >
          {conversation.name}
        </div>
        <div
          className="flex-shrink-0 opacity-0 transition-opacity group-hover:opacity-100"
          onClick={(e) => e.stopPropagation()}
        >
          <Dropdown
            menu={{
              items: [
                {
                  key: "delete",
                  label: "删除",
                  onClick: () => onDelete(conversation.id),
                },
              ],
            }}
            trigger={["click"]}
          >
            <Button
              type="text"
              size="small"
              icon={<MoreIcon className="text-sm text-text-2-icon" />}
              className="flex h-6 w-6 items-center justify-center hover:bg-bg-light-1"
            />
          </Dropdown>
        </div>
      </div>
    </div>
  );
}
