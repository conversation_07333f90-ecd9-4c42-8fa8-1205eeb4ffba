import { Input } from "antd";
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";

import Copy from "@/components/antd/copy";
import { Badge, BadgeVariant } from "@/components/main/badge";
import { Button } from "@/components/main/button";
import { CopyIcon } from "@/components/main/icon";
import SubPageLayout from "@/components/main/layout/components/subPageLayout";
import { useCreateShareToken } from "@/controllers/API/queries/app/useCreateShareToken";
import { useDeleteShareToken } from "@/controllers/API/queries/app/useDeleteShareToken";
import { useGetAppDetail } from "@/controllers/API/queries/app/useGetAppDetail";
import { getAppChatLink } from "@/utils/utils";

export default function AppPublish() {
  const [shareLink, setShareLink] = useState("");

  const { appId = "" } = useParams();

  const { data: appDetail } = useGetAppDetail({ appId });
  const createShareToken = useCreateShareToken();
  const deleteShareToken = useDeleteShareToken();

  useEffect(() => {
    if (appDetail?.share_token) {
      setShareLink(getAppChatLink(appId, appDetail?.share_token));
    }
  }, [appId, appDetail?.share_token]);

  const handlePublish = () => {
    if (!appId) return;
    createShareToken.mutate(
      { app_id: appId },
      {
        onSuccess: (shareToken) => {
          setShareLink(getAppChatLink(appId, shareToken));
        },
      },
    );
  };

  const handleOffline = () => {
    if (!appId) return;
    deleteShareToken.mutate(
      { app_id: appId },
      {
        onSuccess: () => {
          setShareLink("");
        },
      },
    );
  };

  return (
    <SubPageLayout title="应用发布" backPath="/app">
      <div className="mx-auto h-full max-w-[800px] overflow-y-auto py-10">
        <div className="mb-6 flex flex-col gap-3 rounded-lg border border-solid border-border-1 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="text-base font-medium">应用状态</div>
              <Badge
                variant={shareLink ? BadgeVariant.SUCCESS : BadgeVariant.ERROR}
                showDot
              >
                {shareLink ? "已发布" : "已下线"}
              </Badge>
            </div>
            {shareLink ? (
              <Button
                variant="destructive"
                onClick={handleOffline}
                loading={deleteShareToken.isPending}
              >
                下线
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={handlePublish}
                loading={createShareToken.isPending}
              >
                发布
              </Button>
            )}
          </div>
          {shareLink && (
            <div className="text-sm text-text-3">
              应用已发布，用户可以通过以下方式访问。
            </div>
          )}
        </div>
        {shareLink && (
          <div className="rounded-lg border border-solid border-border-1 p-4">
            <div className="mb-4 text-base font-medium">发布方式</div>
            <div className="mb-2 text-sm font-medium">公共链接</div>
            <Input
              className="mb-2"
              value={shareLink}
              suffix={
                <Copy
                  button={
                    <CopyIcon className="cursor-pointer text-sm text-text-2" />
                  }
                  text={shareLink}
                />
              }
              readOnly
            />
            <div className="text-sm text-text-3">
              公共访问已启用，任何人都可以通过上述链接访问你的应用
            </div>
          </div>
        )}
      </div>
    </SubPageLayout>
  );
}
