import { MenuOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";
import { debounce } from "lodash";
import { useEffect, useMemo, useState } from "react";
import { useParams } from "react-router-dom";

import { useDeleteConversation } from "@/controllers/API/queries/app/useDeleteConversation";
import { useGetAppDetail } from "@/controllers/API/queries/app/useGetAppDetail";
import { useGetConversationDetail } from "@/controllers/API/queries/app/useGetConversationDetail";
import { useGetConversations } from "@/controllers/API/queries/app/useGetConversations";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import type { Conversation } from "@/types/app";
import { getColorFromString } from "@/utils/styleUtils";
import ChatPreview from "../components/chatPreview";
import ChatSidebar from "../components/chatSidebar";
import { transformToFormData } from "../utils";

export default function AppChat() {
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [selectedConversationId, setSelectedConversationId] = useState<
    string | undefined
  >();
  const [sidebarVisible, setSidebarVisible] = useState(true);

  const deleteModal = useModal<string>();
  const { appId = "" } = useParams();

  const { data: appDetail } = useGetAppDetail({ appId });
  const { data: conversationList, refetch: refetchConversations } =
    useGetConversations({
      appId,
      get_all: true,
    });
  const { data: conversationDetail } = useGetConversationDetail({
    appId,
    conversationId: selectedConversationId || "",
  });
  const { mutate: deleteConversationMutate } = useDeleteConversation();

  const appData = useMemo(() => {
    if (!appDetail) return undefined;
    return transformToFormData(appDetail);
  }, [appDetail]);

  const appColor = getColorFromString(appId);

  useEffect(() => {
    setConversations(conversationList || []);
  }, [conversationList]);

  // 响应式处理
  useEffect(() => {
    const handleResize = debounce(() => {
      if (window.innerWidth < 768) {
        setSidebarVisible(false);
      } else {
        setSidebarVisible(true);
      }
    }, 200);

    handleResize();
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  const toggleSidebar = () => {
    setSidebarVisible((prev) => !prev);
  };

  const handleDeleteConversation = (conversationId: string) => {
    deleteModal.open(conversationId);
  };

  const handleSelectConversation = (conversationId: string) => {
    setSelectedConversationId(conversationId);
  };

  const selectedConversation = useMemo(() => {
    return conversations.find((conv) => conv.id === selectedConversationId);
  }, [conversations, selectedConversationId]);

  const deleteConversation = () => {
    if (!deleteModal.data) return;
    deleteConversationMutate({
      appId,
      conversationId: deleteModal.data,
    });
    if (selectedConversationId === deleteModal.data) {
      setSelectedConversationId(undefined);
    }
    deleteModal.close();
  };

  const handleCreateConversation = async (conversationId: string) => {
    const res = await refetchConversations();
    if (res.isSuccess) {
      setSelectedConversationId(conversationId);
    }
  };

  return (
    <div className="flex h-full bg-white p-4">
      <div className="relative flex h-full flex-1 overflow-hidden rounded-xl border border-border-1">
        {/* 侧边栏遮罩层，小屏幕下侧边栏展示时才会出现 */}
        {sidebarVisible && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 md:hidden"
            onClick={toggleSidebar}
          />
        )}
        <ChatSidebar
          conversations={conversations}
          selectedConversationId={selectedConversationId}
          visible={sidebarVisible}
          appColor={appColor}
          appName={appDetail?.name || ""}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          onNewConversation={() => setSelectedConversationId(undefined)}
        />
        <div className="flex min-w-0 flex-1 flex-col">
          <div className="flex h-[46px] items-center border-b border-border-1 bg-white px-6">
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={toggleSidebar}
              className="mr-4 md:hidden"
              size="middle"
            />
            <div className="flex-1 truncate text-center text-sm font-medium">
              {selectedConversation?.name}
            </div>
          </div>
          <div className="min-h-0 flex-1">
            <ChatPreview
              data={appData}
              getCompleteData={() => appData}
              hideHeader={true}
              conversation={conversationDetail}
              conversationId={selectedConversationId}
              onCreateConversation={handleCreateConversation}
            />
          </div>
        </div>
      </div>
      <DeleteConfirmModal
        title="确认删除该对话吗？"
        open={deleteModal.visible}
        setOpen={(open) => deleteModal.toggle(open)}
        onConfirm={deleteConversation}
      />
    </div>
  );
}
