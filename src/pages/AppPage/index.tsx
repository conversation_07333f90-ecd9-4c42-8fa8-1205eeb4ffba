import { debounce } from "lodash";
import { useState } from "react";

import EmptyIcon from "@/assets/Empty.svg?react";
import NoResultIcon from "@/assets/noResult.svg?react";
import { Button } from "@/components/main/button";
import { SearchIcon, SquarePlusIcon } from "@/components/main/icon";
import { Input } from "@/components/main/input";
import { useDeleteApp } from "@/controllers/API/queries/app/useDeleteApp";
import { useGetApps } from "@/controllers/API/queries/app/useGetApps";
import { useAlert } from "@/hooks/useAlert";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import NewAppModal from "@/modals/newAppModal";
import type { App } from "@/types/app";
import AppListCard from "./components/appListCard";

export default function AppPage() {
  const [search, setSearch] = useState("");

  const alert = useAlert();
  const newAppModal = useModal<App>();
  const deleteModal = useModal<string>();

  const { data: apps } = useGetApps({ get_all: true });
  const { mutate: deleteApp, isPending: isDeleting } = useDeleteApp();

  const handleEdit = (id: string) => {
    const appData = apps?.find((app) => app.id === id);
    newAppModal.open(appData);
  };

  const handleSearch = debounce(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(event.target.value);
    },
    500,
  );

  const handleDelete = () => {
    if (!deleteModal.data) return;
    deleteApp(
      { app_id: deleteModal.data },
      {
        onSuccess: () => {
          alert.success("删除成功！");
          deleteModal.close();
        },
      },
    );
  };

  return (
    <div className="flex h-full flex-col gap-4">
      <h1 className="text-lg font-semibold">应用管理</h1>
      <div className="flex justify-between">
        <Input
          className="w-80"
          type="text"
          placeholder="搜索应用"
          icon={<SearchIcon className="text-text-3" />}
          onChange={handleSearch}
        />
        <Button onClick={() => newAppModal.open()}>
          <SquarePlusIcon />
          <span>新建 AI 应用</span>
        </Button>
      </div>
      <div className="flex min-h-0 flex-1 items-center justify-center">
        {apps?.length ? (
          <div className="mr-[-0.5rem] grid h-full w-full grid-cols-1 content-start gap-4 overflow-y-auto pr-[0.5rem] sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4">
            {apps.map((item, index) => (
              <AppListCard
                key={index}
                data={item}
                onEdit={handleEdit}
                onDetele={(id) => {
                  deleteModal.open(id);
                }}
              />
            ))}
          </div>
        ) : (
          <div className="text-center">
            {search ? (
              <>
                <NoResultIcon className="mb-2 h-[160px] w-[160px]" />
                <span className="text-sm leading-[22px]">搜索无结果</span>
              </>
            ) : (
              <>
                <EmptyIcon className="mb-2 h-[160px] w-[160px]" />
                <span className="text-sm leading-[22px]">
                  <span>暂无应用，</span>
                  <span
                    className="cursor-pointer text-primary-default"
                    onClick={() => newAppModal.open()}
                  >
                    点击新建
                  </span>
                </span>
              </>
            )}
          </div>
        )}
      </div>
      <NewAppModal
        data={newAppModal.data}
        open={newAppModal.visible}
        setOpen={(open) => newAppModal.toggle(open)}
      />
      <DeleteConfirmModal
        title="确认删除该应用吗？"
        open={deleteModal.visible}
        isDeleting={isDeleting}
        setOpen={(open) => deleteModal.toggle(open)}
        onConfirm={handleDelete}
      />
    </div>
  );
}
