import {
  App,
  AppConfig,
  AppConfigFormData,
  ChatMessage,
  ConversationDetail,
} from "@/types/app";

export function transformToFormData(appData: App): AppConfigFormData {
  return {
    name: appData.name,
    description: appData.description,
    id: appData.id,
    icon: appData.icon,
    model:
      appData.model?.model_name && appData.model?.provider
        ? `${appData.model.model_name}@${appData.model.provider}`
        : "",
    modelSettings: appData.model?.model_settings,
    prompt: appData.prompt,
    openingStatement: appData.opening_statement,
    speechToText: appData.speech_to_text,
    textToSpeech: appData.text_to_speech,
    datasets: appData.dataset?.datasets?.map((item) => ({
      id: item.id,
      name: item.name,
      similarityThreshold: 0.5,
      vectorSimilarityWeight: 0.5,
      topN: 10,
      topK: 50,
      useRerank: true,
      rerankModel: "BGE Rerank v2-M3",
    })),
    memoryWindowSize: appData.memory_window_size,
  };
}

export function transformToAppData(formData: AppConfigFormData): AppConfig {
  return {
    name: formData.name,
    description: formData.description,
    id: formData.id,
    icon: formData.icon,
    model: {
      model_name: formData.model?.split("@")[0],
      provider: formData.model?.split("@")[1],
      model_settings: formData.modelSettings || {
        temperature: 0.7,
        max_tokens: 4096,
      },
    },
    prompt: formData.prompt,
    opening_statement: formData.openingStatement,
    speech_to_text: formData.speechToText,
    text_to_speech: formData.textToSpeech,
    dataset: {
      datasets: formData.datasets,
    },
    memory_window_size: formData.memoryWindowSize,
  };
}

export function transformConversationDetail(data: ConversationDetail) {
  const result: ChatMessage[] = [];
  let prompt: ChatMessage;

  data.messages?.forEach((item, idx) => {
    if (item.sender_name === "User") {
      if (prompt) result.push(prompt);
      prompt = {
        id: `prompt_${new Date().valueOf() + idx}`,
        title: item.text,
        assistantId: item.id,
        answers: [],
      };
    } else if (item.sender_name === "AI") {
      if (prompt) {
        prompt.answers.push({
          id: `answer_${new Date().valueOf() + idx}`,
          content: item.text,
          assistantId: item.id,
        });
      } else {
        prompt = {
          id: `prompt_${new Date().valueOf() + idx}`,
          title: item.text,
          assistantId: item.id,
          answers: [],
        };
      }
    }
    if (prompt && idx === data.messages?.length - 1) result.push(prompt);
  });

  return result;
}
