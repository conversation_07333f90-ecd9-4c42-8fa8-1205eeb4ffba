import { DownOutlined } from "@ant-design/icons";
import { Dropdown } from "antd";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { Button } from "@/components/main/button";
import { ClockIcon } from "@/components/main/icon";
import { useWorkflowStore } from "@/stores/workflowStore";
import { convertUTCToLocalTimezone } from "@/utils/utils";

export default function HeaderControl() {
  const setIsDebugMode = useWorkflowStore((state) => state.setIsDebugMode);

  const handleDebug = () => {
    setIsDebugMode(true);
  };

  const handlePublish = () => {
    console.log("publish");
  };

  return (
    <div className="flex items-center justify-end gap-3">
      <div className="flex items-center gap-1">
        <ClockIcon className="text-base text-text-4" />
        <div className="line-clamp-1 break-all text-xs text-text-3">
          {convertUTCToLocalTimezone("2025-07-27 17:17")}
        </div>
      </div>
      <Badge variant={BadgeVariant.SUCCESS} showDot>
        已发布
      </Badge>
      <Button variant="outline" size="sm" onClick={handleDebug}>
        调试
      </Button>
      <Dropdown.Button
        menu={{
          items: [{ label: "运行", key: "run" }],
        }}
        className="dropdown-button w-auto"
        onClick={handlePublish}
        icon={<DownOutlined />}
        size="small"
      >
        发布
      </Dropdown.Button>
    </div>
  );
}
