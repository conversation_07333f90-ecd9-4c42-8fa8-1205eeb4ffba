import { Handle, HandleType, Position } from "@xyflow/react";

import { cn } from "@/utils/utils";

interface NodeHandleProps {
  type: HandleType;
  className?: string;
}

export default function NodeHandle({ type, className }: NodeHandleProps) {
  return (
    <Handle
      type={type}
      position={type === "source" ? Position.Right : Position.Left}
      className={cn("!h-3 !w-3 !border-border-1 !bg-white", className)}
    />
  );
}
