import { Dropdown, MenuProps } from "antd";

import { MoreIcon } from "@/components/main/icon";
import { NodeType } from "@/constants/enums";
import { useWorkflowStore } from "@/stores/workflowStore";
import NodeIcon from "../../../../nodeIcon";

interface NodeHeaderProps {
  label: string;
  nodeType: NodeType;
  nodeId: string;
}

export default function NodeHeader({ label, nodeType, nodeId }: NodeHeaderProps) {
  const deleteNode = useWorkflowStore((state) => state.deleteNode);
  const copyNode = useWorkflowStore((state) => state.copyNode);

  const handleMenuClick: MenuProps["onClick"] = ({ key }) => {
    switch (key) {
      case "copy":
        copyNode(nodeId);
        break;
      case "delete":
        deleteNode(nodeId);
        break;
      default:
        break;
    }
  };

  const menuItems: MenuProps["items"] = [
    { key: "copy", label: "复制" },
    { key: "delete", label: "删除" },
  ];

  return (
    <div className="flex items-center gap-2">
      <NodeIcon type={nodeType} />
      <div className="flex-1 truncate text-sm font-medium leading-[22px]">
        {label}
      </div>
      <div onClick={(e) => e.stopPropagation()}>
        <Dropdown
          menu={{ items: menuItems, onClick: handleMenuClick }}
          overlayClassName="w-[100px]"
        >
          <MoreIcon className="text-text-2" />
        </Dropdown>
      </div>
    </div>
  );
}
