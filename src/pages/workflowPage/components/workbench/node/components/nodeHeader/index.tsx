import { Dropdown, MenuProps } from "antd";

import { MoreIcon } from "@/components/main/icon";
import { NodeType } from "@/constants/enums";
import NodeIcon from "../../../../nodeIcon";

interface NodeHeaderProps {
  label: string;
  nodeType: NodeType;
}

const menuItems: MenuProps["items"] = [
  { key: "copy", label: "复制" },
  { key: "delete", label: "删除" },
];

export default function NodeHeader({ label, nodeType }: NodeHeaderProps) {
  return (
    <div className="flex items-center gap-2">
      <NodeIcon type={nodeType} />
      <div className="flex-1 truncate text-sm font-medium leading-[22px]">
        {label}
      </div>
      <div onClick={(e) => e.stopPropagation()}>
        <Dropdown menu={{ items: menuItems,  }} overlayClassName="w-[100px]">
          <MoreIcon className="text-text-2" />
        </Dropdown>
      </div>
    </div>
  );
}
