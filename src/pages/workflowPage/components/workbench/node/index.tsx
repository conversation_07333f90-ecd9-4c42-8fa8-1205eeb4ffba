import { NodeProps } from "@xyflow/react";

import { NodeData } from "@/types/workflow";
import { cn } from "@/utils/utils";
import NodeHandle from "./components/nodeHandle";
import NodeHeader from "./components/nodeHeader";

interface GenericNodeProps extends NodeProps {
  data: NodeData;
}

export default function GenericNode({ data, selected }: GenericNodeProps) {
  return (
    <div
      className={cn(
        "group/node flex min-h-[48px] w-[240px] flex-col gap-2 rounded-lg border border-border-1 bg-white p-3 hover:shadow-[0_6px_20px_1px_rgba(117,145,212,0.12)]",
        selected && "border-primary-default",
      )}
    >
      <NodeHeader label={data?.label} nodeType={data?.nodeType} />
      <NodeHandle
        type="source"
        className="group-hover/node:!border-primary-default"
      />
      <NodeHandle
        type="target"
        className="group-hover/node:!border-primary-default"
      />
    </div>
  );
}
