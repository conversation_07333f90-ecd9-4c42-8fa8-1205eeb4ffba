import { ZoomInOutlined, ZoomOutOutlined } from "@ant-design/icons";
import { But<PERSON> } from "antd";

import { SquarePlusIcon } from "@/components/main/icon";
import NodeSelector from "../nodeSelector";

export default function Toolbar() {
  return (
    <div className="pointer-events-auto flex h-10 items-center rounded-md border border-border-1 bg-white p-2 shadow">
      <div className="flex items-center gap-2">
        <ZoomInOutlined className="text-text-2" />
        <span className="text-xs">100%</span>
        <ZoomOutOutlined className="text-text-2" />
      </div>
      <div className="mx-2 h-5 border-r-[1px] border-border-1"></div>
      <NodeSelector>
        <Button type="primary" size="small" className="text-xs">
          <SquarePlusIcon />
          添加节点
        </Button>
      </NodeSelector>
    </div>
  );
}
