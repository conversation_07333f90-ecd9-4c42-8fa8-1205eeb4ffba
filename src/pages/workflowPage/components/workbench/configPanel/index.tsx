import { But<PERSON> } from "antd";
import { useMemo } from "react";

import { CloseIcon } from "@/components/main/icon";
import { NodeType } from "@/constants/enums";
import { useWorkflowStore } from "@/stores/workflowStore";
import ResizablePanel from "../../resizablePanel";
import EndConfigPanel from "./end";
import LLMConfigPanel from "./llm";
import StartConfigPanel from "./start";

const panelComponentMap = {
  [NodeType.START]: StartConfigPanel,
  [NodeType.LLM]: LLMConfigPanel,
  [NodeType.END]: EndConfigPanel,
};

export default function ConfigPanel() {
  const currentWorkflow = useWorkflowStore((state) => state.currentWorkflow);
  const selectedNodeId = useWorkflowStore((state) => state.selectedNodeId);
  const configPanel = useWorkflowStore((state) => state.configPanel);
  const setConfigPanel = useWorkflowStore((state) => state.setConfigPanel);

  const node = currentWorkflow?.nodes.find(
    (node) => node.id === selectedNodeId,
  );

  const PanelComponent = useMemo(() => {
    if (node?.type && panelComponentMap[node?.type]) {
      return panelComponentMap[node?.type];
    }
    return null;
  }, [node?.type]);

  if (!configPanel.isOpen || !node) return null;

  return (
    <ResizablePanel
      width={configPanel.width}
      onWidthChange={(width) => setConfigPanel({ width })}
    >
      <div className="flex items-center justify-between">
        <div>{node?.label}</div>
        <Button
          type="text"
          size="small"
          onClick={() => setConfigPanel({ isOpen: false })}
        >
          <CloseIcon />
        </Button>
      </div>
      <PanelComponent />
    </ResizablePanel>
  );
}
