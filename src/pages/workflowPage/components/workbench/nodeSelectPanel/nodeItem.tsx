import React, { useState } from "react";

import { NodeSelectItem } from "@/types/workflow/node";
import NodeIcon from "../../nodeIcon";

interface NodeItemProps {
  node: NodeSelectItem;
  onClick: (e: React.MouseEvent) => void;
  onDragStart?: (event: React.DragEvent) => void;
}

export default function NodeItem({
  node,
  onClick,
  onDragStart,
}: NodeItemProps) {
  const [isDragging, setIsDragging] = useState(false);

  const handleDragStart = (event: React.DragEvent) => {
    setIsDragging(true);
    onDragStart?.(event);
  };

  const handleDragEnd = () => {
    setIsDragging(false);
  };

  return (
    <div
      className={`flex cursor-pointer select-none items-center gap-3 rounded-md p-1 transition-all duration-200 hover:bg-gray-50 hover:shadow-sm ${isDragging ? "opacity-50" : ""} `}
      onClick={onClick}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      draggable
    >
      <NodeIcon type={node.type} />
      <div className="flex-1 truncate text-sm font-medium">{node.label}</div>
    </div>
  );
}
