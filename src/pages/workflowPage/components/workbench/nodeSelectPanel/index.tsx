import { Divider, Input } from "antd";
import React, { useCallback, useMemo, useState } from "react";

import { NODE_CATEGORIES } from "@/constants/workflow";
import { useNodePosition } from "@/hooks/workflow/useNodePosition";
import { useWorkflowStore } from "@/stores/workflowStore";
import {
  DragTransferData,
  NodeSelectItem,
  SearchState,
} from "@/types/workflow/node";
import {
  filterNodesByKeyword,
  getCategoriesWithFilteredNodes,
} from "@/utils/workflow";
import NodeItem from "./nodeItem";

interface NodeSelectPanelProps {
  onNodeSelect?: (node: NodeSelectItem) => void;
  onClose?: () => void;
  onNodeDragStart?: (node: NodeSelectItem, event: React.DragEvent) => void;
}

export default function NodeSelectPanel({
  onNodeSelect,
  onClose,
  onNodeDragStart,
}: NodeSelectPanelProps) {
  const [searchState, setSearchState] = useState<SearchState>({
    keyword: "",
    filteredNodes: [],
    hasResults: true,
  });

  const addNode = useWorkflowStore((state) => state.addNode);

  const { screenToFlowPosition } = useNodePosition();

  const handleSearchChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const keyword = e.target.value;
      const filteredNodes = filterNodesByKeyword(keyword);

      setSearchState({
        keyword,
        filteredNodes,
        hasResults: filteredNodes.length > 0,
      });
    },
    [],
  );

  const displayCategories = useMemo(() => {
    if (searchState.keyword) {
      return getCategoriesWithFilteredNodes(searchState.filteredNodes);
    }
    return NODE_CATEGORIES;
  }, [searchState.keyword, searchState.filteredNodes]);

  const handleNodeClick = useCallback(
    (node: NodeSelectItem, event: React.MouseEvent) => {
      onNodeSelect?.(node);

      const position = screenToFlowPosition(event.clientX + 240, event.clientY);

      // 添加节点到工作流
      addNode({
        position,
        label: node.label,
        type: node.type,
        data: { label: node.label, nodeType: node.type },
      });

      // 关闭面板
      onClose?.();
    },
    [onNodeSelect, addNode, onClose, screenToFlowPosition],
  );

  const handleNodeDragStart = useCallback(
    (node: NodeSelectItem, event: React.DragEvent) => {
      const dragData: DragTransferData = {
        nodeType: node.type,
        nodeName: node.label,
        nodeData: node,
      };
      // 设置拖拽数据
      event.dataTransfer.setData(
        "application/reactflow",
        JSON.stringify(dragData),
      );
      event.dataTransfer.effectAllowed = "move";

      onNodeDragStart?.(node, event);
    },
    [onNodeDragStart],
  );

  return (
    <div className="w-60 rounded-lg bg-white p-2 pt-3">
      <Input
        placeholder="搜索节点"
        value={searchState.keyword}
        onChange={handleSearchChange}
        className="border-0 shadow-none transition-colors hover:bg-gray-50 focus:shadow-none"
        style={{ boxShadow: "none" }}
        autoFocus
      />
      <Divider size="small" className="border-border-1" />
      <div className="h-[240px] overflow-y-auto">
        {!searchState.hasResults && searchState.keyword ? (
          <div className="flex flex-col items-center justify-center py-12 text-gray-400 duration-300 animate-in fade-in-0">
            <div className="mb-3 text-3xl opacity-60">🔍</div>
            <div className="mb-1 text-sm font-medium text-gray-600">
              未找到匹配的节点
            </div>
          </div>
        ) : (
          displayCategories.map((category) => (
            <div key={category.key} className="mb-2 last:mb-0">
              {category.showTitle && (
                <div className="mb-2 text-sm text-text-4">{category.label}</div>
              )}
              <div className="space-y-1">
                {category.nodes.map((node) => (
                  <NodeItem
                    key={node.id}
                    node={node}
                    onClick={(e) => handleNodeClick(node, e)}
                    onDragStart={(event) => handleNodeDragStart(node, event)}
                  />
                ))}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
