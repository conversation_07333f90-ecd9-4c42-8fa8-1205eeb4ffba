import { MiniMap } from "@xyflow/react";

import ConfigPanel from "../configPanel";
import DebugPanel from "../debugPanel";
import Toolbar from "../toolbar";

export default function CanvasOverlay() {
  return (
    <div className="pointer-events-none absolute left-0 top-0 z-30 flex h-full w-full">
      <div className="relative z-20 flex-1">
        <div className="absolute bottom-0 left-0 flex w-full p-4">
          <MiniMap
            className="pointer-events-auto !relative rounded-lg border border-border-1 shadow"
            style={{
              width: 120,
              height: 90,
              margin: 0,
            }}
          />
          <div className="flex flex-1 items-end justify-center pl-4">
            <Toolbar />
          </div>
        </div>
      </div>
      <div className="pointer-events-auto z-10 flex gap-2 p-2">
        <ConfigPanel />
        <DebugPanel />
      </div>
    </div>
  );
}
