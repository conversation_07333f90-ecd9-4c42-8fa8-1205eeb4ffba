import { useMemo } from "react";

import { NodeType } from "@/constants/enums";
import { NODE_ICON_MAP } from "@/constants/workflow";
import { cn } from "@/utils/utils";

interface NodeIconProps {
  type: NodeType;
  className?: string;
}

export default function NodeIcon({ type, className }: NodeIconProps) {
  const IconComponent = useMemo(() => {
    return NODE_ICON_MAP[type] ? NODE_ICON_MAP[type].component : null;
  }, [type]);

  if (!IconComponent) return null;

  return (
    <span
      className={cn(
        "flex h-6 w-6 flex-shrink-0 items-center justify-center rounded-md text-base",
        className,
      )}
      style={{
        color: NODE_ICON_MAP[type].iconColor,
        backgroundColor: NODE_ICON_MAP[type].bgColor,
      }}
    >
      <IconComponent />
    </span>
  );
}
