import { cn } from "@/utils/utils";
import React, { useRef, useState } from "react";

interface ResizablePanelProps {
  children: React.ReactNode;
  width: number;
  onWidthChange: (width: number) => void;
  minWidth?: number;
  maxWidth?: number;
  className?: string;
  position?: "right" | "left";
}

export default function ResizablePanel({
  children,
  width,
  onWidthChange,
  minWidth = 360,
  maxWidth = 480,
  className,
  position = "right",
}: ResizablePanelProps) {
  const [isDragging, setIsDragging] = useState(false);
  const panelRef = useRef<HTMLDivElement>(null);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    startXRef.current = e.clientX;
    startWidthRef.current = width;

    const handleMouseMove = (e: MouseEvent) => {
      const deltaX =
        position === "right"
          ? startXRef.current - e.clientX // 右侧面板：向左拖动增加宽度
          : e.clientX - startXRef.current; // 左侧面板：向右拖动增加宽度

      const newWidth = Math.max(
        minWidth,
        Math.min(maxWidth, startWidthRef.current + deltaX),
      );

      onWidthChange(newWidth);
    };

    const handleMouseUp = () => {
      setIsDragging(false);
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };

    setIsDragging(true);
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
  };

  return (
    <div
      ref={panelRef}
      className={cn(
        "relative rounded-md border border-border-1 bg-white",
        className,
      )}
      style={{ width: `${width}px` }}
    >
      <div
        className={cn(
          "absolute bottom-0 top-0 w-1 cursor-col-resize transition-colors hover:bg-bg-primary-1",
          position === "right" ? "left-0" : "right-0",
          isDragging && "bg-bg-primary-1",
        )}
        onMouseDown={handleMouseDown}
      />
      <div className="h-full p-3">{children}</div>
    </div>
  );
}
