// Custom Hook to manage theme logic
import { useDarkStore } from "@/stores/darkStore";
import { useEffect, useState } from "react";

const PREFERRED_THEME_KEY = "preferredTheme";
const DEFAULT_THEME = "light";

const useTheme = () => {
  const [systemTheme, setSystemTheme] = useState(false);
  const { setDark, dark } = useDarkStore((state) => ({
    setDark: state.setDark,
    dark: state.dark,
  }));

  const handleSystemTheme = () => {
    if (typeof window !== "undefined") {
      const systemDarkMode = window.matchMedia(
        "(prefers-color-scheme: dark)",
      ).matches;
      setDark(systemDarkMode);
    }
  };

  useEffect(() => {
    // 主题色不跟随系统变化，默认为 light
    const preferredTheme =
      localStorage.getItem(PREFERRED_THEME_KEY) || DEFAULT_THEME;
    if (preferredTheme === "light") {
      setDark(false);
      setSystemTheme(false);
      return;
    }
    if (preferredTheme === "dark") {
      setDark(true);
      setSystemTheme(false);
      return;
    }
    // Default to system theme
    setSystemTheme(true);
    handleSystemTheme();
  }, []);

  useEffect(() => {
    if (systemTheme && typeof window !== "undefined") {
      const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
      const handleChange = (e: MediaQueryListEvent) => {
        setDark(e.matches);
      };
      mediaQuery.addEventListener("change", handleChange);
      return () => {
        mediaQuery.removeEventListener("change", handleChange);
      };
    }
  }, [systemTheme]);

  const setPreferredTheme = (theme: string) => {
    if (theme === "light") {
      setDark(false);
      setSystemTheme(false);
    } else if (theme === "dark") {
      setDark(true);
      setSystemTheme(false);
    } else {
      setSystemTheme(true);
      handleSystemTheme();
    }
    localStorage.setItem(PREFERRED_THEME_KEY, theme);
  };

  return { systemTheme, dark, setPreferredTheme };
};

export default useTheme;
