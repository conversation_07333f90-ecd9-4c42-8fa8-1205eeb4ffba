import * as Form from "@radix-ui/react-form";
import { useEffect, useState } from "react";

import { Input } from "@/components/main/input";
import Modal from "@/components/main/modal";
import { Textarea } from "@/components/main/textarea";
import { useCreateApp } from "@/controllers/API/queries/app/useCreateApp";
import { useUpdateApp } from "@/controllers/API/queries/app/useUpdateApp";
import { useAlert } from "@/hooks/useAlert";
import type { App } from "@/types/app";
import { cn } from "@/utils/utils";

type NewAppModalProps = {
  open: boolean;
  setOpen: (open: boolean) => void;
  data?: App;
  showImage?: boolean;
  onSuccess?: (data: App) => void;
};

export default function NewAppModal({
  open,
  setOpen,
  data,
  showImage = true,
  onSuccess,
}: NewAppModalProps) {
  const alert = useAlert();

  const [name, setName] = useState(data?.name ?? "");
  const [description, setDescription] = useState(data?.description ?? "");

  const { mutate: createApp, isPending: isCreating } = useCreateApp();
  const { mutate: updateApp, isPending: isUpdating } = useUpdateApp();

  const isEdit = !!data;

  useEffect(() => {
    if (open) {
      setName(data?.name ?? "");
      setDescription(data?.description ?? "");
    }
  }, [data?.name, data?.description, open]);

  const handleSubmit = () => {
    if (isEdit) {
      handleUpdate();
    } else {
      handleCreate();
    }
  };

  const handleCreate = () => {
    createApp(
      {
        name,
        description,
      },
      {
        onSuccess: (data) => {
          alert.success("创建成功");
          setOpen(false);
          onSuccess?.(data);
        },
      },
    );
  };

  const handleUpdate = () => {
    if (!data) return;
    updateApp(
      {
        name,
        description,
        app_id: data.id,
        icon: data.icon,
      },
      {
        onSuccess: (data) => {
          alert.success("保存成功");
          setOpen(false);
          onSuccess?.(data);
        },
      },
    );
  };

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      className="h-auto w-[500px]"
      onSubmit={handleSubmit}
    >
      <Modal.Header className="p-0">
        <div
          className={cn(
            "relative flex h-[160px] w-full items-center bg-contain bg-right bg-no-repeat px-6",
            showImage
              ? "h-[160px] bg-[url('/src/assets/app_modal_bg.png')]"
              : "h-[48px]",
          )}
        >
          {isEdit ? "编辑 AI 应用" : "新建 AI 应用"}
          <div className="absolute bottom-0 left-0 h-[1px] w-full bg-gradient-to-r from-[#EFF2FF] to-white"></div>
        </div>
      </Modal.Header>
      <Modal.Content>
        <Form.Field name="name" className="relative pb-6">
          <Form.Label className="mb-2 inline-block text-sm font-medium after:ml-0.5 after:text-red-1 after:content-['*']">
            名称
          </Form.Label>
          <Form.Control asChild>
            <Input
              placeholder="请输入名称，最长24个字符"
              value={name}
              maxLength={24}
              onChange={(e) => setName(e.target.value)}
              required
            />
          </Form.Control>
          <div className="absolute bottom-1 left-0 leading-5">
            <Form.FormMessage
              match="valueMissing"
              className="text-xs font-normal text-red-1"
            >
              请输入名称
            </Form.FormMessage>
          </div>
        </Form.Field>
        <Form.Field name="description">
          <Form.Label className="mb-2 inline-block text-sm font-medium">
            应用描述
          </Form.Label>
          <Form.Control asChild>
            <Textarea
              placeholder="请输入应用描述，最长120字符"
              value={description}
              maxLength={200}
              className="min-h-[120px]"
              onChange={(e) => setDescription(e.target.value)}
            />
          </Form.Control>
        </Form.Field>
      </Modal.Content>
      <Modal.Footer
        submit={{
          submitLabel: isEdit ? "保存" : "新建",
          loading: isCreating || isUpdating,
        }}
      />
    </Modal>
  );
}
