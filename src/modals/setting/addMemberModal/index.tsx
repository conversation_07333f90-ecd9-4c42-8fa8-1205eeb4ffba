import { EyeInvisibleOutlined, EyeTwoTone } from "@ant-design/icons";
import {
  Button,
  Form,
  Input,
  Modal,
  Select,
  TreeSelect,
  type TreeSelectProps,
} from "antd";
import { useEffect, useMemo } from "react";

import { useCreateUser } from "@/controllers/API/queries/tenant/useCreateUser";
import { useUpdateUser } from "@/controllers/API/queries/tenant/useUpdateUser";
import type {
  Organization,
  OrganizationUser,
  UserFormData,
} from "@/types/organization";
import { generateRandomString } from "@/utils/stringManipulation";

const ROLE_OPTIONS = [
  {
    label: "所有者",
    value: "owner",
    description: "能够创建和编辑应用程序以及管理组织架构",
  },
  {
    label: "管理员",
    value: "admin",
    description: "能够创建和编辑应用程序以及管理团队成员",
  },
  {
    label: "成员",
    value: "member",
    description: "能够使用或者创建应用程序, 不可管理团队",
  },
];

export interface AddMemberModalProps {
  teamId: string;
  open: boolean;
  onCancel: () => void;
  onOk: (values: UserFormData) => void;
  data?: OrganizationUser;
  organizationTree?: Organization[];
}

export default function AddMemberModal({
  teamId,
  open,
  onCancel,
  onOk,
  data,
  organizationTree,
}: AddMemberModalProps) {
  const [form] = Form.useForm();

  const { mutateAsync: createUser, isPending: isCreatePending } =
    useCreateUser();
  const { mutateAsync: updateUser, isPending: isUpdatePending } =
    useUpdateUser();

  const isEdit = !!data;

  const treeSelectData = useMemo(() => {
    const convertToTreeSelectData = (
      organizations: Organization[],
    ): TreeSelectProps["treeData"] => {
      return organizations.map((org) => ({
        title: org.name,
        value: org.id,
        key: org.id,
        children: org.children
          ? convertToTreeSelectData(org.children)
          : undefined,
      }));
    };

    return organizationTree ? convertToTreeSelectData(organizationTree) : [];
  }, [organizationTree]);

  useEffect(() => {
    if (!open) return;
    if (data) {
      form.setFieldsValue({
        nickname: data.nickname,
        email: data.email,
        phone: data.phone,
        organization_id: data.organizations[0]?.organization_id,
        role: data.tenant_role,
      });
    } else {
      form.resetFields();
    }
  }, [open, data, form]);

  const generatePassword = () => {
    const password = generateRandomString(8);
    form.setFieldsValue({ password });
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (isEdit) {
        await updateUser({ tenant_id: teamId, ...values, id: data.id });
      } else {
        await createUser({ tenant_id: teamId, ...values });
      }
      form.resetFields();
      onOk(values);
    } catch (error) {
      console.log("表单验证失败:", error);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={isEdit ? "编辑成员" : "添加成员"}
      open={open}
      width={400}
      getContainer={false}
      okText={isEdit ? "保存" : "创建成员"}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={isCreatePending || isUpdatePending}
    >
      <Form form={form} layout="vertical" className="mt-6">
        <Form.Item
          name="nickname"
          label="用户名"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>
        <Form.Item
          name="email"
          label="邮箱"
          rules={[
            { required: true, message: "请输入邮箱" },
            { type: "email", message: "请输入有效的邮箱地址" },
          ]}
        >
          <Input placeholder="请输入邮箱" />
        </Form.Item>
        <Form.Item
          name="phone"
          label="手机号"
          rules={[
            { pattern: /^1[3-9]\d{9}$/, message: "请输入有效的手机号码" },
          ]}
        >
          <Input placeholder="请输入手机号" />
        </Form.Item>
        <Form.Item
          name="organization_id"
          label="部门"
          rules={[{ required: true, message: "请选择部门" }]}
        >
          <TreeSelect
            placeholder="请选择部门"
            treeData={treeSelectData}
            showSearch
            allowClear
            className="w-full"
            treeNodeFilterProp="title"
          />
        </Form.Item>
        <Form.Item
          name="role"
          label="角色"
          rules={[{ required: true, message: "请选择角色" }]}
        >
          <Select
            placeholder="请选择角色"
            options={ROLE_OPTIONS}
            optionRender={({ label, data }) => (
              <div className="flex justify-between text-sm">
                <div>{label}</div>
                <div className="text-[12px] text-text-4-description">
                  {data.description}
                </div>
              </div>
            )}
          />
        </Form.Item>
        {!isEdit && (
          <Form.Item label="初始密码" className="mb-0" required>
            <div className="flex gap-2">
              <Form.Item
                name="password"
                rules={[
                  { required: true, message: "请输入初始密码" },
                  { min: 6, message: "密码长度至少6位" },
                ]}
                className="flex-1"
              >
                <Input.Password
                  placeholder="请输入初始密码"
                  iconRender={(visible) =>
                    visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />
                  }
                />
              </Form.Item>
              <Button onClick={generatePassword}>生成</Button>
            </div>
          </Form.Item>
        )}
      </Form>
    </Modal>
  );
}
