import { Avatar, DatePicker, Form, Input, InputNumber, Modal } from "antd";
import dayjs from "dayjs";
import { useEffect, useState } from "react";

import { EditIcon } from "@/components/main/icon";
import { useCreateTeam } from "@/controllers/API/queries/team/useCreateTeam";
import { useUpdateTeam } from "@/controllers/API/queries/team/useUpdateUser";
import { useModal } from "@/hooks/useModal";
import AvatarModal from "@/modals/avatarModal";
import { TeamData, TeamFormData } from "@/types/team";
import { isValidImageBase64 } from "@/utils/utils";

export interface AddTeamModelProps {
  open: boolean;
  onCancel: () => void;
  onOk: (vaules: TeamData) => void;
  data?: TeamData;
}

export default function AddTeamModel({
  open,
  onCancel,
  onOk,
  data,
}: AddTeamModelProps) {
  const [form] = Form.useForm();
  const avatarModal = useModal();
  const [avatar, setAvatar] = useState("");

  const { mutateAsync: createTeam, isPending: isCreatePending } =
    useCreateTeam();
  const { mutateAsync: updateTeam, isPending: isUpdatePending } =
    useUpdateTeam();

  const isEdit = !!data;

  useEffect(() => {
    if (!open) {
      return;
    }
    form.setFieldsValue({
      name: data?.name ?? "",
      max_user_count: data?.max_user_count ?? 50,
      expire_time: data ? dayjs(data.expire_time) : dayjs().add(1, "year"),
    });
    setAvatar(data?.tenant_picture ?? "");
  }, [open, data, form]);

  const resetForm = () => {
    form.resetFields();
    setAvatar("");
  };

  const handleOk = async () => {
    try {
      const values = await form.validateFields();
      if (isEdit) {
        const updateData: TeamData = {
          ...data,
          name: values.name,
          max_user_count: values.max_user_count,
          expire_time: values.expire_time,
          tenant_picture: avatar,
        };
        await updateTeam(updateData);
      } else {
        const creatData: TeamFormData = {
          ...values,
          avatar: avatar,
        };
        await createTeam(creatData);
      }
      resetForm();
      onOk(values);
    } catch (error) {
      console.log("表单验证失败:", error);
    }
  };

  const handleCancel = () => {
    resetForm();
    onCancel();
  };

  const handleSubmitAvatar = (image: string) => {
    setAvatar(image);
    avatarModal.close();
  };

  const getAvatarContent = () => {
    if (avatar && isValidImageBase64(avatar)) {
      return <img src={avatar} alt="团队头像" />;
    } else {
      return data?.name?.slice(0, 1) || "";
    }
  };

  return (
    <Modal
      title={isEdit ? "编辑团队" : "创建团队"}
      open={open}
      width={400}
      getContainer={false}
      okText={isEdit ? "保存" : "创建团队"}
      onOk={handleOk}
      onCancel={handleCancel}
      confirmLoading={isCreatePending || isUpdatePending}
    >
      <Form form={form} layout="vertical" className="mt-6">
        <Form.Item label="团队名称">
          <div className="flex gap-2">
            <Form.Item
              name="name"
              noStyle
              rules={[
                { required: true, message: "请输入团队名称, 最长24字符" },
              ]}
            >
              <Input placeholder="请输入团队名称, 最长24字符" />
            </Form.Item>
            <div className="group/avatar relative">
              <Avatar
                shape="square"
                className="border-[1px] border-border-1 bg-bg-green-1 text-sm font-medium text-primary-default"
                size={32}
              >
                {getAvatarContent()}
              </Avatar>
              <div className="absolute left-0 top-0 z-10 hidden h-[28px] w-[28px] rounded-[28px] bg-[rgba(0,0,0,0.30)] backdrop-blur-xs group-hover/avatar:block"></div>
              <EditIcon
                className="absolute left-1 top-1 z-20 hidden text-[20px] text-white group-hover/avatar:block"
                onClick={() => avatarModal.open()}
              />
            </div>
          </div>
        </Form.Item>

        <Form.Item
          name="max_user_count"
          label="最大成员数"
          rules={[{ required: true, message: "请输入最大成员数" }]}
        >
          <InputNumber
            min={0}
            placeholder="请输入最大成员数"
            className="w-full"
          />
        </Form.Item>

        <Form.Item
          name="expire_time"
          label="过期时间"
          rules={[{ required: true, message: "请选择过期时间" }]}
        >
          <DatePicker
            disabledDate={(current) => {
              return current && current <= dayjs().endOf("day");
            }}
            className="w-full"
          />
        </Form.Item>
      </Form>
      <AvatarModal
        title=""
        open={avatarModal.visible}
        setOpen={(open) => avatarModal.toggle(open)}
        submitting={avatarModal.loading}
        onSubmit={handleSubmitAvatar}
      />
    </Modal>
  );
}
