import {
  Ava<PERSON>,
  Button,
  Divider,
  Input,
  Pagination,
  Space,
  Table,
  TableProps,
} from "antd";
import dayjs from "dayjs";
import { debounce } from "lodash";
import { useState } from "react";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { SquarePlusIcon } from "@/components/main/icon";
import { useDeleteTeam } from "@/controllers/API/queries/team/useDeleteTeam";
import { useGetTeamData } from "@/controllers/API/queries/team/useGetTeamData";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import AddTeamModel from "@/modals/setting/addTeamModel";
import { TeamData } from "@/types/team";
import { SearchOutlined } from "@ant-design/icons";

const PAGE_SIZE = 20;

interface TeamTableProps {
  onClickTeam: (teamId: string) => void;
}

function TeamTable({ onClickTeam }: TeamTableProps) {
  const [search, setSearch] = useState("");
  const [current, setCurrent] = useState(1);

  const handleSearch = debounce(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      setSearch(event.target.value);
    },
    500,
  );

  const addTeamModel = useModal<TeamData>();
  const deleteTeamModel = useModal<TeamData>();

  const { data: pageData } = useGetTeamData({
    page: current,
    size: PAGE_SIZE,
    name: search,
  });

  const { mutateAsync: deleteTeam, isPending: isDeleting } = useDeleteTeam();

  const getColumns = () => {
    const columns: TableProps<TeamData>["columns"] = [
      {
        title: "团队名称",
        dataIndex: "name",
        key: "name",
        fixed: "left",
        width: 296,
        render: (_, record) => {
          return (
            <div className="flex items-center gap-2">
              <Avatar
                shape="square"
                className="border-[1px] border-border-1 bg-bg-green-1 text-sm font-medium text-primary-default"
                size={24}
              >
                {record.tenant_picture
                  ? record.tenant_picture
                  : record?.name.slice(0, 1)}
              </Avatar>
              <Button
                type="link"
                size="small"
                onClick={() => onClickTeam(record.id)}
              >
                {record.name}
              </Button>
            </div>
          );
        },
      },
      {
        title: "状态",
        dataIndex: "status",
        key: "status",
        width: 100,
        filters: [
          { text: "正常", value: "1" },
          { text: "已过期", value: "0" },
        ],
        onFilter: (value, record) => record.status === value,
        render: (status: "0" | "1") => (
          <Badge
            variant={status === "1" ? BadgeVariant.SUCCESS : BadgeVariant.WARN}
            showDot={true}
          >
            {status === "1" ? "正常" : "已过期"}
          </Badge>
        ),
      },
      {
        title: "成员数量",
        dataIndex: "user_count",
        key: "user_count",
        width: 120,
        render: (_, record) => {
          return (
            <span>
              {record.user_count}/{record.max_user_count}
            </span>
          );
        },
      },
      {
        title: "创建时间",
        key: "create_at",
        dataIndex: "create_at",
        width: 160,
        render: (_, record) => {
          return dayjs(record.create_at).format("YYYY-MM-DD");
        },
      },
      {
        title: "到期时间",
        key: "expire_time",
        dataIndex: "expire_time",
        width: 160,
        render: (_, record) => {
          return record.expire_time
            ? dayjs(record.expire_time).format("YYYY-MM-DD")
            : "--";
        },
      },
      {
        title: "操作",
        key: "action",
        fixed: "right",
        render: (_, record) => {
          return (
            <Space size={0} split={<Divider type="vertical" />}>
              <Button
                type="link"
                size="small"
                className="p-0"
                onClick={() => addTeamModel.open(record)}
              >
                编辑
              </Button>
              <Button
                type="link"
                size="small"
                className="p-0"
                onClick={() => deleteTeamModel.open(record)}
              >
                删除
              </Button>
            </Space>
          );
        },
      },
    ];
    return columns;
  };

  const handlePageChange = (page: number) => {
    setCurrent(page);
  };

  const handleAddTeam = () => {
    addTeamModel.open();
  };

  const handleAddTeamOk = () => {
    addTeamModel.close();
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!deleteTeamModel.data?.id) {
        return;
      }
      await deleteTeam({ tenant_id: deleteTeamModel.data?.id });
      deleteTeamModel.close();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex h-full w-full flex-col">
      <div className="mb-2 flex justify-between">
        <Input
          className="w-60"
          type="text"
          placeholder="搜索团队"
          prefix={<SearchOutlined className="text-text-3" />}
          onChange={handleSearch}
        />

        <Button
          size="middle"
          icon={<SquarePlusIcon className="text-base" />}
          onClick={handleAddTeam}
          className="gap-2 text-sm"
        >
          创建团队
        </Button>
      </div>
      <div className="min-h-0 flex-1 overflow-hidden rounded-md">
        <Table<TeamData>
          columns={getColumns()}
          dataSource={pageData?.items}
          scroll={{ x: 800, y: "calc(100% - 40px)" }}
          size="small"
          className="bg-white"
          pagination={false}
          footer={() => (
            <Pagination
              current={current}
              pageSize={PAGE_SIZE}
              total={pageData?.total}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper={false}
              showTotal={(total) => `共 ${total} 条数据，每页 ${PAGE_SIZE} 条`}
              size="small"
              align="end"
            />
          )}
        />
      </div>
      <AddTeamModel
        data={addTeamModel.data}
        open={addTeamModel.visible}
        onCancel={() => addTeamModel.close()}
        onOk={handleAddTeamOk}
      />
      <DeleteConfirmModal
        title="确认删除该团队吗?"
        description={`即将删除${deleteTeamModel.data?.name}，该操作将同时删除改团队下所有成员，且不可撤销, 请谨慎操作！`}
        open={deleteTeamModel.visible}
        setOpen={(open) => deleteTeamModel.toggle(open)}
        onConfirm={handleDeleteConfirm}
        isDeleting={isDeleting}
      />
    </div>
  );
}

export default TeamTable;
