import { useState } from "react";
import Member from "../member";
import TeamTable from "./teamTable";

function Team() {
  const [curTab, setCurTab] = useState<"team" | "member">("team");
  const [selectedTeam, setSelectTeam] = useState<string>("");

  const handleClick = (teamId: string) => {
    setCurTab("member");
    setSelectTeam(teamId);
  };

  const onBack = () => {
    setCurTab("team");
    setSelectTeam("");
  };

  return (
    <>
      {curTab === "team" ? (
        <div className="flex h-full flex-col">
          <div className="flex h-[48px] items-center px-6 text-base font-medium">
            团队管理
          </div>
          <div className="flex min-h-0 flex-1 gap-4 p-6 pt-3">
            <TeamTable onClickTeam={handleClick} />
          </div>
        </div>
      ) : (
        <Member onBack={onBack} teamId={selectedTeam} />
      )}
    </>
  );
}

export default Team;
