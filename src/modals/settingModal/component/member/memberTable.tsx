import { SearchOutlined } from "@ant-design/icons";
import {
  Button,
  Divider,
  Input,
  Pagination,
  Space,
  Table,
  TableProps,
} from "antd";
import { debounce } from "lodash";
import { useState } from "react";

import { Badge, BadgeVariant } from "@/components/main/badge";
import { SquarePlusIcon } from "@/components/main/icon";
import { Role } from "@/constants/enums";
import { useGetOrganizationUsers } from "@/controllers/API/queries/organization/useGetOrganizationUsers";
import { useDeleteUser } from "@/controllers/API/queries/tenant/useDeleteUser";
import { useModal } from "@/hooks/useModal";
import DeleteConfirmModal from "@/modals/deleteConfirmModal";
import AddMemberModal from "@/modals/setting/addMemberModal";
import MemberInfoModal from "@/modals/setting/memberInfoModal";
import MemberPasswordModal from "@/modals/setting/memberPasswordModal";
import {
  Organization,
  OrganizationNode,
  OrganizationUser,
  UserFormData,
} from "@/types/organization";
import { convertUTCToLocalTimezone } from "@/utils/utils";

const PAGE_SIZE = 20;

const ROLE_MAP = {
  root: "root",
  member: "成员",
  admin: "管理员",
  owner: "所有者",
};

interface MemberTableProps {
  currentOrganization?: OrganizationNode;
  organizationTree?: Organization[];
  editable?: boolean;
  teamId?: string;
}

function MemberTable({
  currentOrganization,
  organizationTree,
  editable,
  teamId,
}: MemberTableProps) {
  const [current, setCurrent] = useState(1);
  const [searchValue, setSearchValue] = useState("");

  const addMemberModal = useModal<OrganizationUser>();
  const memberPasswordModal = useModal<string>();
  const deleteModal = useModal<OrganizationUser>();
  const memberInfoModal = useModal<UserFormData>();

  const { data: pageData } = useGetOrganizationUsers({
    tenant_id: teamId,
    org_id: currentOrganization?.id?.startsWith("root_")
      ? undefined
      : currentOrganization?.id,
    page: current,
    size: PAGE_SIZE,
    search: searchValue,
  });
  const { mutateAsync: deleteUser, isPending: isDeleting } = useDeleteUser();

  const getColumns = () => {
    const columns: TableProps<OrganizationUser>["columns"] = [
      {
        title: "姓名",
        dataIndex: "nickname",
        key: "nickname",
        fixed: "left",
        width: 120,
      },
      {
        title: "邮箱",
        dataIndex: "email",
        key: "email",
        width: 200,
      },
      {
        title: "部门",
        dataIndex: "department",
        key: "department",
        width: 150,
        render: (_, record) => {
          return record.organizations[0]?.organization_name || "--";
        },
      },
      {
        title: "角色",
        key: "tenant_role",
        dataIndex: "tenant_role",
        width: 100,
        render: (text) => <Badge>{ROLE_MAP[text]}</Badge>,
      },
      {
        title: "状态",
        key: "status",
        dataIndex: "status",
        width: 100,
        render: (status: "0" | "1") => (
          <Badge
            variant={status === "1" ? BadgeVariant.SUCCESS : BadgeVariant.WARN}
            showDot={true}
          >
            {status === "1" ? "有效" : "待激活"}
          </Badge>
        ),
      },
      {
        title: "加入时间",
        key: "create_at",
        dataIndex: "create_at",
        width: 180,
        render: (text) => convertUTCToLocalTimezone(text),
      },
    ];

    if (editable) {
      columns.push({
        title: "操作",
        key: "action",
        width: 150,
        fixed: "right",
        render: (_, record) => {
          const isAction =
            record.tenant_role === Role.ADMIN ||
            record.tenant_role === Role.OWNER;
          return (
            <Space size={0} split={<Divider type="vertical" />}>
              <Button
                type="link"
                size="small"
                className="p-0"
                onClick={() => addMemberModal.open(record)}
                disabled={isAction}
              >
                编辑
              </Button>
              <Button
                type="link"
                size="small"
                className="p-0"
                onClick={() => memberPasswordModal.open(record.id)}
                disabled={isAction}
              >
                改密
              </Button>
              <Button
                type="link"
                size="small"
                className="p-0"
                onClick={() => deleteModal.open(record)}
                disabled={isAction}
              >
                删除
              </Button>
            </Space>
          );
        },
      });
    }

    return columns;
  };

  const handleSearch = debounce((value: string) => {
    setSearchValue(value);
    // 这里可以添加搜索逻辑
  }, 500);

  const handleAddMember = () => {
    addMemberModal.open();
  };

  const handleAddMemberOk = (values: UserFormData) => {
    const isEdit = !!addMemberModal.data;
    addMemberModal.close();
    if (!isEdit) {
      // 添加成功后打开账号信息弹窗
      memberInfoModal.open(values);
    }
  };

  const handlePageChange = (page: number) => {
    setCurrent(page);
  };

  const handleDeleteConfirm = async () => {
    try {
      if (!deleteModal.data?.id) return;
      await deleteUser({
        tenant_id: teamId || "",
        user_id: deleteModal.data.id,
      });
      deleteModal.close();
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <div className="flex h-full w-full flex-col">
      {/* 表格头部 */}
      <div className="mb-2 flex h-6 items-center justify-between">
        <span className="text-base font-medium">
          {currentOrganization?.name}
        </span>
        <div style={{ display: "flex", gap: "12px", alignItems: "center" }}>
          <Input
            className="h-8 w-[240px]"
            placeholder="搜索提示..."
            prefix={<SearchOutlined style={{ color: "#bfbfbf" }} />}
            onChange={(e) => handleSearch(e.target.value)}
            allowClear
            size="small"
          />
          {editable && (
            <Button
              onClick={handleAddMember}
              size="middle"
              className="text-sm"
              icon={<SquarePlusIcon className="text-base" />}
            >
              添加成员
            </Button>
          )}
        </div>
      </div>
      {/* 表格 */}
      <div className="flex min-h-0 flex-1 flex-col overflow-hidden rounded-md">
        <Table<OrganizationUser>
          columns={getColumns()}
          dataSource={pageData?.items}
          scroll={{ x: 800, y: "calc(100% - 40px)" }}
          size="small"
          style={{
            backgroundColor: "#fff",
          }}
          pagination={false}
          footer={() => (
            <Pagination
              current={current}
              pageSize={PAGE_SIZE}
              total={pageData?.total}
              onChange={handlePageChange}
              showSizeChanger={false}
              showQuickJumper={false}
              showTotal={(total) => `共 ${total} 条数据，每页 ${PAGE_SIZE} 条`}
              size="small"
              align="end"
            />
          )}
        />
      </div>
      <AddMemberModal
        teamId={teamId || ""}
        data={addMemberModal.data}
        open={addMemberModal.visible}
        onCancel={() => addMemberModal.close()}
        onOk={handleAddMemberOk}
        organizationTree={organizationTree}
      />
      <MemberPasswordModal
        teamId={teamId || ""}
        open={memberPasswordModal.visible}
        onOk={() => memberPasswordModal.close()}
        onCancel={() => memberPasswordModal.close()}
        userId={memberPasswordModal.data || ""}
      />
      <DeleteConfirmModal
        title="确认删除该成员吗？"
        description={`即将删除${deleteModal.data?.username}，该操作不可逆，请谨慎操作！`}
        open={deleteModal.visible}
        setOpen={(open) => deleteModal.toggle(open)}
        onConfirm={handleDeleteConfirm}
        isDeleting={isDeleting}
      />
      <MemberInfoModal
        data={memberInfoModal.data}
        open={memberInfoModal.visible}
        onOk={() => memberInfoModal.close}
        onCancel={() => memberInfoModal.close()}
      />
    </div>
  );
}

export default MemberTable;
