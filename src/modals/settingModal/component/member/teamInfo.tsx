import { Avatar } from "antd";

import { useModal } from "@/hooks/useModal";
import AvatarModal from "@/modals/avatarModal";
import TeamNameModal from "@/modals/teamNameModal";
import useAuthStore from "@/stores/authStore";

interface TeamInfoProps {
  teamName?: string;
}

export default function TeamInfo({ teamName }: TeamInfoProps) {
  const userData = useAuthStore((state) => state.userData);

  const avatarModal = useModal();
  const teamNameModal = useModal();

  const handleSubmitAvatar = () => {
    avatarModal.close();
  };

  return (
    <div className="flex h-[52px] items-center gap-2 px-6">
      <div className="group/avatar relative">
        <Avatar
          className="border-[1.4px] border-border-1 bg-bg-green-1 text-sm font-medium text-primary-default"
          size={28}
        >
          {(teamName || userData?.current_organization_name)?.slice(0, 1)}
        </Avatar>
        {/* 暂时隐藏编辑头像功能 */}
        {/* <div className="absolute left-0 top-0 z-10 hidden h-[28px] w-[28px] rounded-[28px] bg-[rgba(0,0,0,0.30)] backdrop-blur-xs group-hover/avatar:block"></div>
        <EditIcon
          className="absolute left-1 top-1 z-20 hidden text-[20px] text-white group-hover/avatar:block"
          onClick={() => avatarModal.open()}
        /> */}
      </div>

      <div className="group/name flex items-center gap-2 text-sm font-medium">
        <span>{teamName ? teamName : userData?.current_tenant_name}</span>

        {/* 暂时隐藏编辑名称功能 */}
        {/* <EditIcon
          className="hidden text-[20px] text-text-2 group-hover/name:block"
          onClick={() => teamNameModal.open()}
        /> */}
      </div>
      <AvatarModal
        title="修改团队 logo"
        open={avatarModal.visible}
        setOpen={(open) => avatarModal.toggle(open)}
        submitting={avatarModal.loading}
        onSubmit={handleSubmitAvatar}
      />
      <TeamNameModal
        open={teamNameModal.visible}
        setOpen={(open) => teamNameModal.toggle(open)}
      />
    </div>
  );
}
