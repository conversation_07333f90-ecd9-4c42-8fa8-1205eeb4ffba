import { useState } from "react";

import { Button } from "@/components/main/button";
import { LeftArrowIcon } from "@/components/main/icon";
import { Role } from "@/constants/enums";
import { useGetOrganizationTree } from "@/controllers/API/queries/organization/useGetOrganizationTree";
import useAuthStore from "@/stores/authStore";
import { OrganizationNode } from "@/types/organization";
import { ADMIN_ROLES } from "@/utils/permission";
import MemberList from "./memberTable";
import OrganizationTree from "./organizationTree";
import TeamInfo from "./teamInfo";

interface MemberProps {
  onBack?: () => void;
  teamId?: string;
}

function Member({ onBack, teamId }: MemberProps) {
  const [currentOrganization, setCurrentOrganization] = useState<
    OrganizationNode | undefined
  >(undefined);

  const userData = useAuthStore((state) => state.userData);
  const teams = useAuthStore((state) => state.userData?.tenants);
  const teamName = teams?.find((item) => item.tenant_id === teamId)?.name;
  const editable = ADMIN_ROLES.includes(userData?.role || Role.MEMBER);

  const { data: treeData } = useGetOrganizationTree({ tenant_id: teamId });

  return (
    <div className="flex h-full flex-col">
      <div className="flex h-[48px] items-center gap-3 px-6 text-base font-medium">
        {teamId ? (
          <div className="flex items-center gap-3">
            <Button variant="outline" size="iconSm" onClick={onBack}>
              <LeftArrowIcon className="text-text-2" />
            </Button>
            <div className="h-4 border-l-[1px] border-border-1"></div>
          </div>
        ) : (
          ""
        )}
        成员管理
      </div>
      <TeamInfo teamName={teamName} />
      <div className="flex min-h-0 flex-1 gap-4 p-6 pt-3">
        <div className="h-full w-[200px] flex-shrink-0">
          <OrganizationTree
            selectedNode={currentOrganization}
            onSelectNode={setCurrentOrganization}
            treeData={treeData}
            editable={editable}
            teamId={teamId || ""}
            teamName={teamName || ""}
          />
        </div>
        <div className="h-full min-w-0 flex-1">
          <MemberList
            teamId={teamId}
            currentOrganization={currentOrganization}
            organizationTree={treeData}
            editable={editable}
          />
        </div>
      </div>
    </div>
  );
}

export default Member;
