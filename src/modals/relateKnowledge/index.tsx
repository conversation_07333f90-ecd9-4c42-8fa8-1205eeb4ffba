import { Checkbox } from "antd";
import { useEffect, useState } from "react";

import { Button } from "@/components/main/button";
import Modal from "@/components/main/modal";
import { useGetKnowledges } from "@/controllers/API/queries/knowledge/useGetKnowledges";
import type { KnowledgeType } from "@/types/knowledge";
import { knowledgeIconMap } from "@/utils/styleUtils";

interface RelateKnowledgeModalProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  onSubmit: (selectedKnowledges: KnowledgeType[]) => void;
}

export default function RelateKnowledgeModal({
  open,
  setOpen,
  onSubmit,
}: RelateKnowledgeModalProps) {
  const [selectedKnowledges, setSelectedKnowledges] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  // 获取知识库列表
  const { data: knowledges, isLoading } = useGetKnowledges({
    page: 1,
    page_size: 100,
  });

  const knowledgeList = knowledges?.kbs || [];

  // 处理全选
  useEffect(() => {
    if (knowledgeList.length > 0) {
      setSelectAll(selectedKnowledges.length === knowledgeList.length);
    }
  }, [selectedKnowledges, knowledgeList]);

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedKnowledges(knowledgeList.map((item) => item.id));
    } else {
      setSelectedKnowledges([]);
    }
    setSelectAll(checked);
  };

  const handleSelectKnowledge = (knowledgeId: string, checked: boolean) => {
    if (checked) {
      setSelectedKnowledges((prev) => [...prev, knowledgeId]);
    } else {
      setSelectedKnowledges((prev) => prev.filter((id) => id !== knowledgeId));
    }
  };

  const renderKnowledgeIcon = (knowledge: KnowledgeType) => {
    const Icon =
      knowledge.kb_type === "yuque"
        ? knowledgeIconMap.YuqueIcon
        : knowledgeIconMap.FolderIcon;
    return <Icon className="h-9 w-9" />;
  };

  // 重置状态当模态框关闭时
  useEffect(() => {
    if (!open) {
      setSelectedKnowledges([]);
      setSelectAll(false);
    }
  }, [open]);

  const handleCancel = () => {
    setOpen(false);
  };

  const handleConfirm = () => {
    const selectedKnowledgeList = knowledgeList.filter((knowledge) =>
      selectedKnowledges.includes(knowledge.id),
    );
    onSubmit(selectedKnowledgeList);
    setOpen(false);
  };

  return (
    <Modal className="h-auto w-[500px]" open={open} setOpen={setOpen}>
      <Modal.Header>关联知识库</Modal.Header>
      <Modal.Content>
        <div className="mb-3 text-sm">
          从现有知识库中选择要添加到当前 Agent 的知识库：
        </div>
        <div className="max-h-[400px] overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-text-2">加载中...</div>
            </div>
          ) : knowledgeList.length === 0 ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-text-2">暂无知识库</div>
            </div>
          ) : (
            knowledgeList.map((knowledge) => (
              <div
                key={knowledge.id}
                className="mb-1 flex cursor-pointer items-center gap-2 rounded-lg border border-border-1 p-2 pr-3"
                onClick={() =>
                  handleSelectKnowledge(
                    knowledge.id,
                    !selectedKnowledges.includes(knowledge.id),
                  )
                }
              >
                <Checkbox checked={selectedKnowledges.includes(knowledge.id)} />
                <div className="min-w-0 flex-1">
                  <div className="truncate text-sm text-text-1">
                    {knowledge.name}
                  </div>
                  <div className="truncate text-xs text-text-4">
                    {knowledge.description || "知识库描述"}
                  </div>
                </div>
                <div className="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-lg bg-bg-light-3">
                  {renderKnowledgeIcon(knowledge)}
                </div>
              </div>
            ))
          )}
        </div>
      </Modal.Content>
      <Modal.Footer>
        <div className="flex w-full items-center justify-between">
          <Checkbox
            checked={selectAll}
            indeterminate={
              selectedKnowledges.length > 0 &&
              selectedKnowledges.length < knowledgeList.length
            }
            onChange={(e) => handleSelectAll(e.target.checked)}
          >
            全选
          </Checkbox>
          <div className="flex gap-3">
            <Button variant="outline" onClick={handleCancel}>
              取消
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={selectedKnowledges.length === 0}
            >
              添加已选知识库 ({selectedKnowledges.length})
            </Button>
          </div>
        </div>
      </Modal.Footer>
    </Modal>
  );
}
