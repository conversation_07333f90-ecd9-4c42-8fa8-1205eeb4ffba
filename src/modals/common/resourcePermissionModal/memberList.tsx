import { <PERSON>tar, But<PERSON>, Divider, Modal, Select, Tooltip } from "antd";
import { useMemo } from "react";

import { SquarePlusIcon, WarningIcon } from "@/components/main/icon";
import { Member, PermissionType } from "@/constants/enums";
import { useModal } from "@/hooks/useModal";
import { SelectedMember } from "@/types/permission";
import PermissionMemberModal from "../permissionMemberModal";
import { PERMISSION_TYPE_OPTIONS } from "./helper";

interface MemberListProps {
  data: SelectedMember[];
  updateMembers: (members: SelectedMember[]) => void;
}

export default function MemberList({ data, updateMembers }: MemberListProps) {
  const addMemberModal = useModal();

  const initialSelectedMembers: SelectedMember[] = useMemo(() => {
    return data.map((member) => ({
      id: member.id,
      name: member.name,
      type: Member.USER,
      permission: PermissionType.EDITABLE,
      isReadonly: true, // 现有成员设为只读
    }));
  }, [data]);

  const handleRemove = (id: string) => {
    Modal.confirm({
      title: "确定移除该成员吗？",
      content:
        "移除后，该成员将无法访问当前内容或相关权限配置，且需重新邀请后才能恢复访问。",
      okText: "移除",
      cancelText: "暂不",
      icon: (
        <WarningIcon className="mr-3 flex-shrink-0 text-2xl text-[#FBB310]" />
      ),
      onOk: () => {
        updateMembers(data.filter((member) => member.id !== id));
      },
    });
  };

  const handleAddMemberConfirm = (selectedMembers: SelectedMember[]) => {
    updateMembers(selectedMembers);
  };

  const PERMISSION_TYPE_OPTIONS_WITH_TOOLTIP = PERMISSION_TYPE_OPTIONS.map(
    (option) => ({
      ...option,
      label: (
        <Tooltip title={option.description} placement="left">
          <span>{option.label}</span>
        </Tooltip>
      ),
    }),
  );

  return (
    <div className="mt-3">
      <div className="flex gap-2">
        <div className="font-medium">已选择成员</div>
        <Button
          size="small"
          className="gap-0.5 text-xs"
          onClick={() => addMemberModal.open()}
        >
          <SquarePlusIcon className="text-sm" />
          添加成员
        </Button>
      </div>
      <div>
        {data.map((item) => {
          return (
            <div
              key={item.id}
              className="mt-2 flex h-[28px] items-center justify-between"
            >
              <div className="flex flex-1 items-center gap-2">
                <Avatar
                  className="bg-bg-green-1 text-[8px] font-medium text-primary-default"
                  size={16}
                >
                  {item.name.slice(0, 1)}
                </Avatar>
                <div>{item.name}</div>
                {/* <Badge variant={BadgeVariant.PROCESSING}>所有者</Badge> */}
              </div>
              <Select
                className="w-[90px] flex-shrink-0"
                value={item.permission}
                options={PERMISSION_TYPE_OPTIONS_WITH_TOOLTIP}
                onChange={(value) => {
                  updateMembers(
                    data.map((member) =>
                      member.id === item.id
                        ? { ...member, permission: value }
                        : member,
                    ),
                  );
                }}
                popupRender={(menu) => {
                  return (
                    <>
                      {menu}
                      <Divider className="my-1" />
                      <div
                        className="flex h-8 cursor-pointer items-center rounded-md px-3"
                        onClick={() => handleRemove(item.id)}
                      >
                        移除
                      </div>
                    </>
                  );
                }}
              />
            </div>
          );
        })}
      </div>
      <PermissionMemberModal
        open={addMemberModal.visible}
        onClose={() => addMemberModal.close()}
        onConfirm={handleAddMemberConfirm}
        initialSelectedMembers={initialSelectedMembers}
      />
    </div>
  );
}
