import { UserOutlined } from "@ant-design/icons";
import { Avatar, Select, Tooltip } from "antd";

import EmptyIcon from "@/assets/Empty.svg?react";
import { UserGroupIcon } from "@/components/main/icon";
import { Member, PermissionType } from "@/constants/enums";
import { SelectedMember } from "@/types/permission";
import { PERMISSION_TYPE_OPTIONS } from "../resourcePermissionModal/helper";

interface SelectedMembersProps {
  selectedMembers: SelectedMember[];
  onPermissionChange: (memberId: string, permission: PermissionType) => void;
  onRemoveMember: (memberId: string) => void;
}

export default function SelectedMembers({
  selectedMembers,
  onPermissionChange,
  onRemoveMember,
}: SelectedMembersProps) {
  const PERMISSION_TYPE_OPTIONS_WITH_TOOLTIP = PERMISSION_TYPE_OPTIONS.map(
    (option) => ({
      ...option,
      label: (
        <Tooltip title={option.description} placement="left">
          <span>{option.label}</span>
        </Tooltip>
      ),
    }),
  );

  return (
    <>
      <div className="mb-2 text-sm font-medium">已选择成员</div>
      <div className="flex-1 overflow-y-auto">
        {selectedMembers.map((member) => (
          <div
            key={member.id}
            className="flex h-9 items-center gap-2 rounded px-2 hover:bg-gray-50"
          >
            {member.type === Member.DEPARTMENT ? (
              <UserGroupIcon className="text-base text-primary-default" />
            ) : (
              <Avatar size={16} icon={<UserOutlined />} />
            )}
            <span className="flex-1 text-sm">{member.name}</span>
            <Select
              value={member.permission}
              onChange={(value: PermissionType | "remove") => {
                if (value === "remove") {
                  onRemoveMember(member.id);
                } else {
                  onPermissionChange(member.id, value);
                }
              }}
              disabled={member.isReadonly}
              size="small"
              className="w-20"
              options={[
                ...PERMISSION_TYPE_OPTIONS_WITH_TOOLTIP,
                { label: "移除", value: "remove" },
              ]}
            />
          </div>
        ))}
        {selectedMembers.length === 0 && (
          <div className="flex h-full flex-col items-center justify-center">
            <EmptyIcon className="mb-2 h-[120px] w-[120px]" />
            <span className="text-sm leading-[22px]">暂无指定人员</span>
          </div>
        )}
      </div>
    </>
  );
}
