import { <PERSON><PERSON>, Modal } from "antd";
import { useEffect, useMemo, useState } from "react";

import { Member, PermissionType } from "@/constants/enums";
import { useGetOrganizationMembers } from "@/controllers/API/queries/organization/useGetOrganizationMembers";
import useAuthStore from "@/stores/authStore";
import { OrganizationMember } from "@/types/organization";
import { SelectedMember } from "@/types/permission";

import OrganizationStructure, { BreadcrumbItem } from "./organizationStructure";
import SelectedMembers from "./selectedMembers";

interface PermissionMemberModalProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: (selectedMembers: SelectedMember[]) => void;
  initialSelectedMembers?: SelectedMember[];
}

interface CachedOrganizationData {
  members: OrganizationMember[];
  timestamp: number;
}

// 数据缓存时间（5分钟）
const CACHE_DURATION = 5 * 60 * 1000;

const ROOT_ORGANIZATION_ID = "root";

export default function PermissionMemberModal({
  open,
  onClose,
  onConfirm,
  initialSelectedMembers = [],
}: PermissionMemberModalProps) {
  const [searchValue, setSearchValue] = useState("");
  const [currentPath, setCurrentPath] = useState<BreadcrumbItem[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<SelectedMember[]>([]);
  const [checkedNodes, setCheckedNodes] = useState<Set<string>>(new Set());
  const [currentOrganizationId, setCurrentOrganizationId] = useState<
    string | undefined
  >(undefined);

  // 缓存组织数据
  const [organizationCache, setOrganizationCache] = useState<
    Map<string, CachedOrganizationData>
  >(new Map());

  const userData = useAuthStore((state) => state.userData);

  // 检查是否有缓存数据
  const hasCachedData = useMemo(() => {
    const cacheKey = currentOrganizationId || ROOT_ORGANIZATION_ID;
    const cached = organizationCache.get(cacheKey);
    return cached && Date.now() - cached.timestamp < CACHE_DURATION;
  }, [currentOrganizationId, organizationCache]);

  // 获取当前组织的成员列表，当 org_id 为空时，获取根组织列表
  const { data: organizationMembers, isLoading } = useGetOrganizationMembers(
    { org_id: currentOrganizationId },
    {
      enabled: open && !hasCachedData,
    },
  );

  useEffect(() => {
    if (!open) return;
    setSelectedMembers(initialSelectedMembers);
    setCheckedNodes(new Set(initialSelectedMembers.map((m) => m.id)));
  }, [open, initialSelectedMembers]);

  // 初始化根组织信息
  useEffect(() => {
    if (open && currentPath.length === 0) {
      // 当弹窗打开且路径为空时，初始化为根组织，org_id 为 undefined 时获取根组织列表
      setCurrentOrganizationId(undefined);
      setCurrentPath([
        {
          id: ROOT_ORGANIZATION_ID,
          name: userData?.current_tenant_name || "组织架构",
        },
      ]);
    }
  }, [open, currentPath.length, userData?.current_tenant_name]);

  // 缓存数据更新
  useEffect(() => {
    if (!organizationMembers) return;
    const cacheKey = currentOrganizationId || ROOT_ORGANIZATION_ID;
    setOrganizationCache((prev) => {
      const newCache = new Map(prev);
      newCache.set(cacheKey, {
        members: organizationMembers,
        timestamp: Date.now(),
      });
      return newCache;
    });
  }, [organizationMembers, currentOrganizationId]);

  // 显示的成员列表
  const displayMembers = useMemo(() => {
    const cacheKey = currentOrganizationId || ROOT_ORGANIZATION_ID;
    const cached = organizationCache.get(cacheKey);
    let members: OrganizationMember[] = [];

    if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
      members = cached.members;
    } else {
      members = organizationMembers || [];
    }

    if (!searchValue.trim()) {
      return members;
    }

    // 根据搜索内容进行过滤
    return members.filter((member) =>
      member.name.toLowerCase().includes(searchValue.toLowerCase()),
    );
  }, [
    currentOrganizationId,
    organizationCache,
    organizationMembers,
    searchValue,
  ]);

  const handleNodeClick = (member: OrganizationMember) => {
    if (member.type === Member.DEPARTMENT && member.has_children) {
      setCurrentPath((prev) => [...prev, { id: member.id, name: member.name }]);
      setCurrentOrganizationId(member.id);
      setSearchValue("");
    }
  };

  const handleBreadcrumbClick = (targetId: string) => {
    const targetIndex = currentPath.findIndex((item) => item.id === targetId);
    if (targetIndex !== -1) {
      const newPath = currentPath.slice(0, targetIndex + 1);
      setCurrentPath(newPath);

      // 如果点击的是根目录，则设置为 undefined 以获取根组织列表
      const orgId = targetId === ROOT_ORGANIZATION_ID ? undefined : targetId;
      setCurrentOrganizationId(orgId);
      setSearchValue("");
    }
  };

  const isNodeChecked = (nodeId: string) => {
    return checkedNodes.has(nodeId);
  };

  const isNodeDisabled = (nodeId: string) => {
    // 如果是外部传入的只读成员，不可编辑
    const initialMember = initialSelectedMembers.find((m) => m.id === nodeId);
    return initialMember?.isReadonly || false;
  };

  const handleNodeCheck = (member: OrganizationMember, checked: boolean) => {
    if (isNodeDisabled(member.id)) return;

    const newCheckedNodes = new Set(checkedNodes);
    const newSelectedMembers = [...selectedMembers];

    if (checked) {
      // 选中节点
      newCheckedNodes.add(member.id);
      if (!selectedMembers.find((m) => m.id === member.id)) {
        newSelectedMembers.push({
          id: member.id,
          name: member.name,
          type: member.type,
          permission: PermissionType.EDITABLE,
          isReadonly: false,
        });
      }
    } else {
      // 取消选中节点
      newCheckedNodes.delete(member.id);
      const index = newSelectedMembers.findIndex((m) => m.id === member.id);
      if (
        index !== -1 &&
        !initialSelectedMembers.find((m) => m.id === member.id)
      ) {
        newSelectedMembers.splice(index, 1);
      }
    }

    setCheckedNodes(newCheckedNodes);
    setSelectedMembers(newSelectedMembers);
  };

  const handlePermissionChange = (
    memberId: string,
    permission: PermissionType,
  ) => {
    setSelectedMembers((prev) =>
      prev.map((member) =>
        member.id === memberId ? { ...member, permission } : member,
      ),
    );
  };

  const handleRemoveMember = (memberId: string) => {
    // 不能移除外部传入的只读成员
    const member = selectedMembers.find((m) => m.id === memberId);
    if (member?.isReadonly) return;

    setSelectedMembers((prev) => prev.filter((m) => m.id !== memberId));
    setCheckedNodes((prev) => {
      const newSet = new Set(prev);
      newSet.delete(memberId);
      return newSet;
    });
  };

  const handleSave = () => {
    onConfirm?.(selectedMembers);
    resetState();
    onClose();
  };

  const handleCancel = () => {
    resetState();
    onClose();
  };

  const resetState = () => {
    setSelectedMembers(initialSelectedMembers);
    setCheckedNodes(new Set(initialSelectedMembers.map((m) => m.id)));
    setSearchValue("");
    setCurrentPath([]);
    setCurrentOrganizationId(undefined);
  };

  return (
    <Modal
      title="添加指定成员"
      open={open}
      onCancel={handleCancel}
      width={900}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button key="save" type="primary" onClick={handleSave}>
          保存
        </Button>,
      ]}
      className="permission-member-modal"
    >
      <div className="flex h-[500px] py-6">
        <div className="flex w-1/2 flex-col border-r border-border-1 pr-3">
          <OrganizationStructure
            title="组织架构"
            isLoading={isLoading}
            searchValue={searchValue}
            onSearchChange={setSearchValue}
            currentPath={currentPath}
            onBreadcrumbClick={handleBreadcrumbClick}
            members={displayMembers}
            onNodeClick={handleNodeClick}
            isNodeChecked={isNodeChecked}
            isNodeDisabled={isNodeDisabled}
            onNodeCheck={handleNodeCheck}
          />
        </div>
        <div className="flex w-1/2 flex-col pl-3">
          <SelectedMembers
            selectedMembers={selectedMembers}
            onPermissionChange={handlePermissionChange}
            onRemoveMember={handleRemoveMember}
          />
        </div>
      </div>
    </Modal>
  );
}
