import { UserOutlined } from "@ant-design/icons";
import { Avatar, Breadcrumb, Checkbox, Input } from "antd";
import { useMemo } from "react";

import {
  RightArrowIcon,
  SearchIcon,
  TeamIcon,
  UserGroupIcon,
} from "@/components/main/icon";
import { OrganizationMember } from "@/types/organization";
import { cn } from "@/utils/utils";

export interface BreadcrumbItem {
  id: string;
  name: string;
}

interface OrganizationStructureProps {
  title?: string;
  isLoading: boolean;
  searchValue: string;
  onSearchChange: (value: string) => void;

  currentPath: BreadcrumbItem[];
  onBreadcrumbClick: (targetId: string) => void;

  members: OrganizationMember[];
  onNodeClick: (member: OrganizationMember) => void;

  isNodeChecked: (nodeId: string) => boolean;
  isNodeDisabled: (nodeId: string) => boolean;
  onNodeCheck: (member: OrganizationMember, checked: boolean) => void;
}

export default function OrganizationStructure({
  title = "组织架构",
  isLoading,
  searchValue,
  onSearch<PERSON>hange,
  currentPath,
  onBreadcrumbClick,
  members,
  onNodeClick,
  isNodeChecked,
  isNodeDisabled,
  onNodeCheck,
}: OrganizationStructureProps) {
  const hasSearch = useMemo(() => searchValue.trim().length > 0, [searchValue]);

  return (
    <>
      <div className="mb-2 text-sm font-medium">{title}</div>
      <Input
        placeholder="搜索组织或人员"
        prefix={<SearchIcon className="text-base text-text-3" />}
        value={searchValue}
        onChange={(e) => onSearchChange(e.target.value)}
        allowClear
      />
      {currentPath.length > 0 && (
        <div className="flex h-8 items-center gap-2 px-2">
          <TeamIcon className="text-base text-primary-default" />
          <Breadcrumb separator="/">
            {currentPath.map((item, index) => (
              <Breadcrumb.Item key={item.id}>
                {index < currentPath.length - 1 ? (
                  <a
                    onClick={() => onBreadcrumbClick(item.id)}
                    className="cursor-pointer text-blue-600 hover:text-blue-800"
                  >
                    {item.name}
                  </a>
                ) : (
                  <span className="text-gray-500">{item.name}</span>
                )}
              </Breadcrumb.Item>
            ))}
          </Breadcrumb>
        </div>
      )}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex h-full items-center justify-center">
            <div className="text-sm text-gray-500">加载中...</div>
          </div>
        ) : members.length > 0 ? (
          members.map((member) => {
            const isChecked = isNodeChecked(member.id);
            const isDisabled = isNodeDisabled(member.id);
            const hasChildren =
              member.type === "department" && member.has_children;

            return (
              <div
                key={member.id}
                className={cn(
                  "flex items-center gap-2 rounded px-2 py-[5px] hover:bg-gray-50",
                  isDisabled && "opacity-50",
                )}
              >
                <Checkbox
                  checked={isChecked}
                  disabled={isDisabled}
                  onChange={(e) => onNodeCheck(member, e.target.checked)}
                />
                <div className="flex flex-1 items-center gap-2">
                  {member.type === "department" ? (
                    <UserGroupIcon className="text-base text-primary-default" />
                  ) : (
                    <Avatar size={16} icon={<UserOutlined />} />
                  )}
                  <span
                    className={cn(
                      "flex-1 text-sm",
                      hasChildren &&
                        !hasSearch &&
                        "cursor-pointer hover:text-blue-600",
                    )}
                    onClick={() =>
                      !hasSearch &&
                      hasChildren &&
                      !isDisabled &&
                      onNodeClick(member)
                    }
                  >
                    {member.name}
                  </span>
                  {hasChildren && !hasSearch && (
                    <RightArrowIcon
                      className="cursor-pointer text-xs text-text-2"
                      onClick={() => !isDisabled && onNodeClick(member)}
                    />
                  )}
                </div>
              </div>
            );
          })
        ) : (
          <div className="flex h-full items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-sm">暂无数据</div>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
