import { CircleInfoIcon } from "@/components/main/icon";
import { CustomLink } from "@/customization/components/custom-link";
import { Transition } from "@headlessui/react";
import { useEffect, useState } from "react";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import IconComponent from "../../components/common/genericIconComponent";
import { NoticeAlertType } from "../../types/alerts";

export default function NoticeAlert({
  title,
  list = [],
  id,
  link,
  removeAlert,
}: NoticeAlertType): JSX.Element {
  const [show, setShow] = useState(true);

  useEffect(() => {
    if (show) {
      setTimeout(() => {
        setShow(false);
        setTimeout(() => {
          removeAlert(id);
        }, 300);
      }, 3000);
    }
  }, [id, removeAlert, show]);

  const handleClose = () => {
    setShow(false);
    setTimeout(() => {
      removeAlert(id);
    }, 300);
  };

  return (
    <Transition
      appear
      show={show}
      enter="transition-[transform,opacity] duration-300 ease-out"
      enterFrom={"transform translate-y-[-100%] opacity-0"}
      enterTo={"transform translate-y-0 opacity-100"}
      leave="transition-[transform,opacity] duration-300 ease-in"
      leaveFrom={"transform translate-y-0 opacity-100"}
      leaveTo={"transform translate-y-[-100%] opacity-0"}
    >
      <div
        onClick={handleClose}
        className="mb-6 max-w-[400px] rounded-xl border border-primary-default bg-[#e9edff] p-3 shadow-[0px_5px_22px_0px_rgba(61,68,110,0.20)]"
      >
        <div className="flex gap-2">
          <div className="flex-shrink-0 cursor-help">
            <CircleInfoIcon className="h-5 w-5 text-primary-default" />
          </div>
          <div className="flex-1">
            <div
              className="line-clamp-6 text-sm font-normal leading-[22px] word-break-break-word"
              title={title}
            >
              {title}
            </div>
            {link && (
              <p className="mt-3 text-sm">
                <CustomLink
                  to={link}
                  className="whitespace-nowrap font-medium text-info-foreground hover:text-accent-foreground"
                >
                  Details
                </CustomLink>
              </p>
            )}
          </div>
        </div>
      </div>
    </Transition>
  );
}
