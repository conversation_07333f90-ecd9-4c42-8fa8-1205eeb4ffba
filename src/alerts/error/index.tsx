import { CircleCloseIcon } from "@/components/main/icon";
import { Transition } from "@headlessui/react";
import { useEffect, useState } from "react";
import Markdown from "react-markdown";
import remarkGfm from "remark-gfm";
import { ErrorAlertType } from "../../types/alerts";

export default function ErrorAlert({
  title,
  list = [],
  id,
  removeAlert,
}: ErrorAlertType): JSX.Element {
  const [show, setShow] = useState(true);

  useEffect(() => {
    if (show) {
      setTimeout(() => {
        setShow(false);
        setTimeout(() => {
          removeAlert(id);
        }, 300);
      }, 3000);
    }
  }, [id, removeAlert, show]);

  const handleClose = () => {
    setShow(false);
    setTimeout(() => {
      removeAlert(id);
    }, 300);
  };

  return (
    <Transition
      appear
      show={show}
      enter="transition-[transform,opacity] duration-300 ease-out"
      enterFrom={"transform translate-y-[-100%] opacity-0"}
      enterTo={"transform translate-y-0 opacity-100"}
      leave="transition-[transform,opacity] duration-300 ease-in"
      leaveFrom={"transform translate-y-0 opacity-100"}
      leaveTo={"transform translate-y-[-100%] opacity-0"}
    >
      <div
        onClick={handleClose}
        className="mb-6 max-w-[400px] rounded-xl border border-red-1 bg-[#fff0ef] p-3 shadow-[0px_5px_22px_0px_rgba(61,68,110,0.20)]"
      >
        <div className="flex gap-2">
          <div className="flex-shrink-0">
            <CircleCloseIcon className="text-xl text-red-1" />
          </div>
          <div className="flex-1">
            <div
              className="line-clamp-6 text-sm font-normal leading-[22px] word-break-break-word"
              title={title}
            >
              {title}
            </div>
            {list?.length !== 0 &&
            list?.some((item) => item !== null && item !== undefined) ? (
              <div className="mt-2 text-sm text-error-foreground">
                <ul className="list-disc space-y-1 pl-5 align-top">
                  {list.map((item, index) => (
                    <li key={index} className="word-break-break-word">
                      <span className="">
                        <Markdown
                          linkTarget="_blank"
                          remarkPlugins={[remarkGfm]}
                          className="align-text-top"
                          components={{
                            a: ({ node, ...props }) => (
                              <a
                                href={props.href}
                                target="_blank"
                                className="underline"
                                rel="noopener noreferrer"
                              >
                                {props.children}
                              </a>
                            ),
                            p({ node, ...props }) {
                              return (
                                <span className="inline-block w-fit max-w-full align-text-top">
                                  {props.children}
                                </span>
                              );
                            },
                          }}
                        >
                          {Array.isArray(item) ? item.join("\n") : item}
                        </Markdown>
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            ) : (
              <></>
            )}
          </div>
        </div>
      </div>
    </Transition>
  );
}
