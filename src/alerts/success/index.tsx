import { CircleCheckFilledIcon } from "@/components/main/icon";
import { Transition } from "@headlessui/react";
import { useEffect, useState } from "react";
import { SuccessAlertType } from "../../types/alerts";

export default function SuccessAlert({
  title,
  id,
  removeAlert,
}: SuccessAlertType): JSX.Element {
  const [show, setShow] = useState(true);

  useEffect(() => {
    if (show) {
      setTimeout(() => {
        setShow(false);
        setTimeout(() => {
          removeAlert(id);
        }, 300);
      }, 3000);
    }
  }, [id, removeAlert, show]);

  const handleClose = () => {
    setShow(false);
    setTimeout(() => {
      removeAlert(id);
    }, 300);
  };

  return (
    <Transition
      appear
      show={show}
      enter="transition-[transform,opacity] duration-300 ease-out"
      enterFrom={"transform translate-y-[-100%] opacity-0"}
      enterTo={"transform translate-y-0 opacity-100"}
      leave="transition-[transform,opacity] duration-300 ease-in"
      leaveFrom={"transform translate-y-0 opacity-100"}
      leaveTo={"transform translate-y-[-100%] opacity-0"}
    >
      <div
        onClick={handleClose}
        className="mb-6 max-w-[400px] rounded-xl border border-[#11D7B2] bg-[#E7FBF7] p-3 shadow-[0px_5px_22px_0px_rgba(61,68,110,0.20)]"
      >
        <div className="flex gap-2">
          <div className="flex-shrink-0">
            <CircleCheckFilledIcon className="h-5 w-5 text-[#11D7B2]" />
          </div>
          <div className="flex-1">
            <div
              className="line-clamp-6 text-sm font-normal leading-[22px] word-break-break-word"
              title={title}
            >
              {title}
            </div>
          </div>
        </div>
      </div>
    </Transition>
  );
}
