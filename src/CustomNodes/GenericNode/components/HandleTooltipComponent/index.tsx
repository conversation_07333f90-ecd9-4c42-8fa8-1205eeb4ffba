import { convertTestName } from "@/components/common/storeCardComponent/utils/convert-test-name";
import { Badge } from "@/components/main/badge";
import { useTranslate } from "@/locales";
import { nodeColorsName } from "@/utils/styleUtils";

export default function HandleTooltipComponent({
  isInput,
  tooltipTitle,
  isConnecting,
  isCompatible,
  isSameNode,
  left,
}: {
  isInput: boolean;
  tooltipTitle: string;
  isConnecting: boolean;
  isCompatible: boolean;
  isSameNode: boolean;
  left: boolean;
}) {
  const { t } = useTranslate("workFlow");
  const tooltips = tooltipTitle.split("\n");
  const plural = tooltips.length > 1 ? "s" : "";

  return (
    <div className="">
      {isSameNode ? (
        t("common.sameNodeTooltip")
      ) : (
        <div className="flex items-center gap-1">
          {isConnecting ? (
            isCompatible ? (
              <span>
                <span className="font-semibold">{t("editor.connect")}</span>
              </span>
            ) : (
              <span>{t("common.incompatibleWith")}</span>
            )
          ) : (
            <span className="text-sm">
              {isInput
                ? `${t("siderBar.inputs")}${plural} ${t("editor.type")} :`
                : `${t("siderBar.outputs")}${plural} ${t("editor.type")} :`}
            </span>
          )}
          {tooltips.map((word, index) => (
            <Badge
              className="h-6 rounded-xl px-2 py-0"
              key={`${index}-${word.toLowerCase()}`}
              style={{
                backgroundColor: left
                  ? `hsl(var(--datatype-${nodeColorsName[word]}))`
                  : `hsl(var(--datatype-${nodeColorsName[word]}-foreground))`,
                color: left
                  ? `hsl(var(--datatype-${nodeColorsName[word]}-foreground))`
                  : `hsl(var(--datatype-${nodeColorsName[word]}))`,
              }}
              data-testid={`${isInput ? "input" : "output"}-tooltip-${convertTestName(word)}`}
            >
              {word}
            </Badge>
          ))}
          {isConnecting && (
            <span>
              {isInput ? t("siderBar.inputs") : t("siderBar.outputs")}
            </span>
          )}
        </div>
      )}
      {!isConnecting && (
        <div className="mt-1 flex flex-col gap-0.5 text-sm leading-5">
          <div>
            <b>{t("common.drag")}</b> {t("common.dragToConnect")}
          </div>
          <div>
            <b>{t("common.click")}</b> {t("common.clickToFilter")}
          </div>
        </div>
      )}
    </div>
  );
}
