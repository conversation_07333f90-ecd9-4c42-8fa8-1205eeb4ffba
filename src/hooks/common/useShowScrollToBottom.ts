import { throttle } from "lodash";
import { useEffect, useRef, useState } from "react";

/**
 * 用于判断是否需要显示滚动到底部的按钮
 * @param containerElement 滚动容器元素
 * @param distance 距离底部多远时显示
 * @returns 是否需要显示滚动到底部的按钮
 */
export function useShowScrollToBottom(
  containerElement?: HTMLDivElement | null,
  distance = 80,
) {
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const showScrollRef = useRef(showScrollToBottom);

  useEffect(() => {
    showScrollRef.current = showScrollToBottom;
  }, [showScrollToBottom]);

  useEffect(() => {
    const scrollListener = throttle(() => {
      if (!containerElement) return;

      const { scrollTop, clientHeight, scrollHeight } = containerElement;
      const bottomDistance = scrollHeight - scrollTop - clientHeight;
      const shouldShow = bottomDistance > distance;

      if (shouldShow !== showScrollRef.current) {
        setShowScrollToBottom(shouldShow);
      }
    }, 300);

    containerElement?.addEventListener("scroll", scrollListener);

    return () => {
      containerElement?.removeEventListener("scroll", scrollListener);
    };
  }, [containerElement, distance]);

  return showScrollToBottom;
}
