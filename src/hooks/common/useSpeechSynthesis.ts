import { useRef, useState } from "react";

/**
 * 实现语音播放功能
 */
export function useSpeechSynthesis<T>() {
  const [data, setData] = useState<T | undefined>(undefined);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const currentUtteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

  const isSupported = !!(
    typeof window !== "undefined" && window.speechSynthesis
  );

  const speak = (text: string, speakData?: T) => {
    if (!isSupported) return;

    // 如果正在播放，先停止
    if (window.speechSynthesis.speaking) {
      window.speechSynthesis.cancel();
    }

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = "zh-CN";

    // 添加事件监听器
    utterance.onstart = () => {
      setIsSpeaking(true);
      setData(speakData);
    };

    utterance.onend = () => {
      setIsSpeaking(false);
      setData(undefined);
      currentUtteranceRef.current = null;
    };

    utterance.onerror = () => {
      setIsSpeaking(false);
      setData(undefined);
      currentUtteranceRef.current = null;
    };

    currentUtteranceRef.current = utterance;
    window.speechSynthesis.speak(utterance);
  };

  const cancel = () => {
    if (!isSupported) return;
    setIsSpeaking(false);
    setData(undefined);
    window.speechSynthesis.cancel();
    currentUtteranceRef.current = null;
  };

  return {
    isSupported,
    isSpeaking,
    data,
    speak,
    cancel,
  };
}
