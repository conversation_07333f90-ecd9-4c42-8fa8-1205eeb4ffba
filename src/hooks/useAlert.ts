import useAlertStore from "@/stores/alertStore";

export function useAlert() {
  const setSuccessData = useAlertStore((state) => state.setSuccessData);
  const setErrorData = useAlertStore((state) => state.setErrorData);
  const setNoticeData = useAlertStore((state) => state.setNoticeData);

  const success = (title: string) => {
    setSuccessData({ title });
  };

  const error = (title: string, list?: Array<string>) => {
    setErrorData({ title, list });
  };

  const notice = (title: string, link?: string) => {
    setNoticeData({ title, link });
  };

  return {
    success,
    error,
    notice,
  };
}
