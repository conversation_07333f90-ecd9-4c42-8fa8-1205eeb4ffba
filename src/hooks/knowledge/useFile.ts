import { FileItem } from "@/types/knowledge";
import { useState } from "react";

export function useFile() {
  const [files, setFiles] = useState<FileItem[]>([]);

  const addFiles = (newFiles: FileItem[]) => {
    setFiles((prev) => [...prev, ...newFiles]);
  };

  const removeFile = (id: string) => {
    setFiles((prev) => prev.filter((file) => file.id !== id));
  };

  const clearFiles = () => {
    setFiles([]);
  };

  const updateFile = (id: string, fileProps: Partial<FileItem>) => {
    setFiles((prev) =>
      prev.map((file) => (file.id === id ? { ...file, ...fileProps } : file)),
    );
  };

  return {
    files,
    addFiles,
    removeFile,
    clearFiles,
    updateFile,
  };
}
