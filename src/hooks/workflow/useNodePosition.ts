import { NodePosition } from "@/types/workflow/node";
import { useReactFlow } from "@xyflow/react";
import { useCallback } from "react";

/**
 * 节点位置计算
 */
export function useNodePosition() {
  const reactFlowInstance = useReactFlow();

  /**
   * 屏幕坐标转换为画布坐标
   */
  const screenToFlowPosition = useCallback(
    (screenX: number, screenY: number): NodePosition => {
      if (!reactFlowInstance) {
        return { x: screenX, y: screenY };
      }

      const position = reactFlowInstance.screenToFlowPosition({
        x: screenX,
        y: screenY,
      });
      return { x: position.x, y: position.y };
    },
    [reactFlowInstance],
  );

  return {
    screenToFlowPosition,
  };
}
