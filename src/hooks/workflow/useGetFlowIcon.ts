import { useEffect, useMemo, useState } from "react";

import { useGetTemplateStyle } from "@/pages/MainPage/utils/get-template-style";
import { FlowType } from "@/types/flow";
import { swatchColors } from "@/utils/styleUtils";
import { getNumberFromString } from "@/utils/utils";

export function useGetFlowIcon({ flowData }: { flowData: FlowType }) {
  const [icon, setIcon] = useState<string>("");

  const { getIcon } = useGetTemplateStyle(flowData);

  const iconColor = useMemo(() => {
    const index =
      (flowData.gradient && !isNaN(parseInt(flowData.gradient))
        ? parseInt(flowData.gradient)
        : getNumberFromString(flowData.gradient ?? flowData.id)) %
      swatchColors.length;
    return swatchColors[index];
  }, [flowData.gradient, flowData.id]);

  useEffect(() => {
    getIcon().then(setIcon);
  }, [getIcon]);

  return { icon, iconColor };
}
