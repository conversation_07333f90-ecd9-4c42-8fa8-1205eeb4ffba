const AssemblyAISVG = (props) => (
  <svg
    width="1.1em"
    height="1.1em"
    viewBox="0 0 501 434"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M221.202 0.944641C189.435 0.944641 160.93 20.4579 149.437 50.0725L0.462402 433.945H113.632L230.894 131.791H230.943C233.886 124.427 241.085 119.224 249.5 119.224C257.915 119.224 265.114 124.427 268.057 131.791H283.681V70.5011H254.679L281.673 0.944641H221.202Z"
      fill="#213ED7"
    />
    <path
      d="M149.445 50.0726C160.471 21.6619 187.153 2.54782 217.352 1.04075L217.315 0.944641H279.722C311.489 0.944641 339.993 20.4579 351.486 50.0725L500.461 433.945H385.356L240.893 61.6995C232.622 43.4564 214.251 30.7668 192.917 30.7668C171.53 30.7668 153.122 43.5188 144.88 61.834L149.445 50.0726Z"
      fill="#566DE8"
    />
  </svg>
);
export default AssemblyAISVG;
