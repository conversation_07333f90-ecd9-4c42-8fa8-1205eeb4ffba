const SvgAirbyte = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlSpace="preserve"
    style={{
      enableBackground: "new 0 0 841.89 595.28",
    }}
    viewBox="0 0 841.89 595.28"
    width="1em"
    height="1em"
    {...props}
  >
    <path
      d="M349.6 124.45c48.94-54.99 129.98-71.12 196.61-39.38 88.52 42.17 120.82 149.6 72.62 232.48L510.41 503.76c-6.06 10.41-16.03 18-27.72 21.11a45.99 45.99 0 0 1-34.64-4.51l131.26-225.49c34.97-60.15 11.58-138.11-52.6-168.8-48.16-23.03-107.02-11.53-142.6 28.08-19.62 21.74-30.64 49.82-31.01 79.01-.37 29.2 9.94 57.53 29.01 79.76 3.43 3.99 7.12 7.75 11.04 11.25l-76.63 131.88c-3 5.16-6.99 9.67-11.74 13.3a45.845 45.845 0 0 1-15.97 7.82 46.098 46.098 0 0 1-17.77 1.16 46.124 46.124 0 0 1-16.87-5.68l83.19-143.17a164.492 164.492 0 0 1-25.29-56.58l-50.97 87.9c-6.06 10.41-16.03 18-27.72 21.11a45.99 45.99 0 0 1-34.64-4.51l131.83-226.76a168.65 168.65 0 0 1 19.03-26.19zm152.16 72.18c31.75 18.21 42.71 58.7 24.34 90.22L399.69 503.74c-6.06 10.41-16.03 18-27.72 21.11a45.99 45.99 0 0 1-34.64-4.51l117.38-201.93a66.675 66.675 0 0 1-26.01-11.65 66.228 66.228 0 0 1-18.7-21.4 65.82 65.82 0 0 1-7.95-27.22c-.64-9.54.82-19.1 4.27-28.02 3.45-8.92 8.8-17 15.7-23.67a66.558 66.558 0 0 1 24.24-14.95c9.08-3.18 18.74-4.37 28.32-3.49s18.85 3.82 27.18 8.62zm-45.98 40.76c-2.17 1.66-4 3.72-5.36 6.08h-.01a20.613 20.613 0 0 0-2.75 11.71c.27 4.09 1.76 8 4.27 11.25s5.94 5.69 9.84 7c3.91 1.32 8.12 1.45 12.1.39 3.99-1.06 7.56-3.27 10.28-6.35 2.72-3.08 4.46-6.89 5-10.95s-.15-8.19-1.97-11.87a20.806 20.806 0 0 0-8.28-8.78 20.964 20.964 0 0 0-15.83-2.07c-2.64.72-5.12 1.93-7.29 3.59z"
      style={{
        fillRule: "evenodd",
        clipRule: "evenodd",
        fill: "#505aa5",
      }}
    />
  </svg>
);
export default SvgAirbyte;
