import { InputN<PERSON>ber, Slider } from "antd";
import type { SliderSingleProps } from "antd/es/slider";

import { cn } from "@/utils/utils";

import "./index.less";

interface SliderWithInputProps extends SliderSingleProps {
  label?: string;
  size?: "small" | "default";
}

export default function SliderWithInput({
  label,
  min,
  max,
  value,
  className,
  size,
  onChange,
  ...props
}: SliderWithInputProps) {
  return (
    <div
      className={cn(
        "slider-with-input relative",
        size === "small" ? "slider-with-input--small" : "",
        className,
      )}
    >
      <div className="flex min-h-[28px] items-center justify-between">
        <span className="slider-with-input__label">{label}</span>
        <InputNumber
          className={cn(size === "small" ? "w-[50px]" : "w-[60px]")}
          value={value}
          onChange={(value) => onChange?.(Number(value))}
          min={min}
          max={max}
          size={size === "small" ? "small" : "middle"}
        />
      </div>
      <Slider
        min={min}
        max={max}
        value={value}
        onChange={onChange}
        {...props}
      />
    </div>
  );
}
