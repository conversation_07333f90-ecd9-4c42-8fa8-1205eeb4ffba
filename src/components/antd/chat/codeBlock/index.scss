$headerEle: ".dtc__aigc__codeblock__header";
$toolEle: ".dtc__aigc__codeblock__tool";

.dtc__aigc__codeblock {
  margin: 16px 0;
  background: #fff;
  border-radius: 4px;
  border: 1px solid #ebecf0;
  > pre {
    &::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }
    &::-webkit-scrollbar-track {
      cursor: pointer;
      width: 6px;
      background: transparent;
      border-radius: 2em;
    }
    &::-webkit-scrollbar-thumb {
      cursor: pointer;
      background-color: rgba(#101f1c, 0.3);
      background-clip: padding-box;
      min-height: 28px;
      border-radius: 2em;
    }
    &::-webkit-scrollbar-track:hover {
      width: 10px;
      background: rgba(#101f1c, 0.1);
    }
    &::-webkit-scrollbar-thumb:hover {
      background-color: rgba(#101f1c, 0.5);
    }
  }
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px;
    background: #fff;
    border-bottom: 1px solid #ebecf0;
    border-radius: 4px 4px 0 0;
  }
  &__language {
    color: #3d446e;
    line-height: 20px;
  }
  &__tool {
    display: flex;
    gap: 4px;
    color: #8b8fa8;
    font-size: 16px;
    &:hover {
      .dtc__icon {
        color: #1d78ff;
      }
    }
  }
  &--convert {
    background: #f5f5f8;
    border-color: #d8dae2;

    #{$headerEle} {
      background: #f5f5f8;
      border-color: #d8dae2;
    }

    #{$toolEle} {
      color: #b1b4c5;
    }
  }
}
