.dtc__chat__textarea__container {
  width: 100%;
  border-radius: 8px;
  border: 1px solid #d8dae2;
  background: #fff;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  .dtc__chat__textarea {
    border: none;
    background-color: transparent;
    color: #3d446e;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding: 8px 36px 8px 16px;
    &:focus {
      box-shadow: none;
    }
  }
  .dtc__chat__textarea__send {
    font-size: 24px;
    position: absolute;
    bottom: 8px;
    right: 8px;
    &:hover:not(&--disabled) {
      &.dtc__icon path {
        fill: url(#paint0_linear_3878_6538_hover);
      }
    }
    &--disabled {
      cursor: not-allowed;
      color: #b1b4c5;
    }
  }
}
