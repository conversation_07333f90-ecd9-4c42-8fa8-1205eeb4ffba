.dtc__aigc__loading {
  display: flex;
  gap: 2px;
  width: 100%;
  padding: 10px 0;
  align-items: center;
  justify-content: center;
  height: 28px;
  > div {
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background-color: #e8f1ff;
    &:nth-child(1) {
      animation: 0.6s linear 0s infinite alternate loading;
    }
    &:nth-child(2) {
      animation: 0.6s linear 0.2s infinite alternate loading;
    }
    &:nth-child(3) {
      animation: 0.6s linear 0.4s infinite alternate loading;
    }
  }
}

@keyframes loading {
  0% {
    width: 4px;
    height: 4px;
    background-color: #fff;
  }
  50% {
    width: 5px;
    height: 5px;
    background-color: #bbd6ff;
  }
  100% {
    width: 6px;
    height: 6px;
    background-color: #1d78ff;
  }
}
