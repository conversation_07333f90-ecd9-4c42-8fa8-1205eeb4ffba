.dtc__aigc__markdown {
  word-break: break-all;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue",
    <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
  h1 {
    font-size: 32px;
    line-height: 40px;
    margin-block-end: 8px;
  }
  h2 {
    font-size: 24px;
    line-height: 32px;
    margin-block-end: 8px;
  }
  h3 {
    font-size: 20px;
    line-height: 28px;
    margin-block-end: 8px;
  }
  h4 {
    font-size: 18px;
    line-height: 26px;
    margin-block-end: 8px;
  }
  h5 {
    font-size: 16px;
    line-height: 24px;
    margin-block-end: 8px;
  }
  h6 {
    font-size: 14px;
    line-height: 22px;
    margin-block-end: 8px;
  }
  p {
    line-height: 22px;
    font-size: 14px;
    margin-block-end: 8px;
  }
  ol,
  ul {
    font-size: 14px;
    line-height: 22px;
    margin-block-start: 0;
    margin-block-end: 8px;
    margin-inline-start: 0;
    margin-inline-end: 0;
    padding-inline-start: 20px;
  }
  ul {
    list-style-type: disc;
    ul {
      list-style-type: circle;
      margin-block-end: 4px;
    }
  }
  li {
    list-style-type: inherit;
    margin-block-end: 4px;
    line-height: 22px;
    &::marker {
      font-size: 14px;
    }
  }
  &__inlineCode {
    margin: 0 4px;
    padding: 2px 8px;
    background: #fff;
    border-radius: 4px;
    border: 1px solid rgba(6, 14, 26, 0.08);
    word-break: keep-all;
  }
  &__hr {
    margin: 8px;
    border-top: none;
  }
  > *:last-child {
    margin-bottom: 0;
  }
  &--blink {
    > *:last-child::after {
      content: "_";
      transform: translate(1px, 2px) scale(2, 1);
      font-weight: normal;
      color: inherit;
      display: inline-block;
      visibility: hidden;
      animation: blinker 1s step-end infinite;
    }

    @keyframes blinker {
      0% {
        visibility: inherit;
      }
      50% {
        visibility: hidden;
      }
      100% {
        visibility: inherit;
      }
    }
  }
}
