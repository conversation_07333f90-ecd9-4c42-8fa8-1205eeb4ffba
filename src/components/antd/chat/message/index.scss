.dtc__message {
  &__container {
    display: flex;
    gap: 4px;
    position: relative;
    &:hover {
      .dtc__message__iconGroup {
        opacity: 1;
      }
    }
  }
  &__avatar {
    font-size: 16px;
    height: 20px;
    padding: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &__wrapper {
    width: 100%;
    overflow: hidden;
  }
  &__content {
    padding: 0 8px;
    &--loading {
      width: 100%;
    }
  }
  &__footer {
    &:not(:empty) {
      margin-top: 8px;
      display: flex;
      align-items: center;
      gap: 4px;
    }
  }
  &__stopText {
    color: #b1b4c5;
    font-size: 12px;
    line-height: 20px;
  }
  &__extra {
    text-align: center;
  }
  &__stopBtn {
    &.ant-btn {
      margin: 4px auto 0;
      .dtc__icon {
        margin-right: 4px;
      }
    }
  }
  &__iconGroup {
    position: absolute;
    bottom: 0;
    left: 32px;
    transform: translateY(24px);
    opacity: 0;
    padding-top: 4px;
    z-index: 99;
  }
  &__icon {
    cursor: pointer;
    color: #8b8fa8;
    font-size: 16px;
    &:hover {
      color: #0a67f2;
    }
    &:active {
      color: #005ce6;
    }
  }
}
