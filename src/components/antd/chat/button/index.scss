$primaryGradient:
  #05a9fe 0%,
  #055dff 50%,
  #8b3ffe 100%;
$primaryGradientHover:
  #05a9fe 0%,
  #055dff 50%,
  #8b3ffe 100%;

.ant-btn.dtc__aigc__button {
  border: none;
  border-radius: 8px;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  &[disabled] {
    color: #b1b4c5;
    background-color: #ebecf0;
    border-color: #ebecf0;
  }
  &--primary {
    &:not(.ant-btn[disabled]) {
      background: linear-gradient(110deg, $primaryGradient);
      color: #fff;
    }
    &:hover {
      &:not(.ant-btn[disabled]) {
        background: linear-gradient(110deg, $primaryGradientHover);
      }
    }
  }
  &--default {
    &:not(.ant-btn[disabled]) {
      border: 1px solid #d8dae2;
      color: #3d446e;
      transition: background-image 0.3s ease;
    }
    &:hover {
      &:not(.ant-btn[disabled]) {
        border-color: transparent;
        background-image:
          linear-gradient(white, white),
          linear-gradient(110deg, $primaryGradientHover);
        background-origin: border-box;
        background-clip: padding-box, border-box;
        svg,
        path {
          fill: url(#secondary_linear_gradient_hover);
        }
        .dtc__aigc__button__text {
          background-image: linear-gradient(110deg, $primaryGradientHover);
          background-clip: text;
          color: transparent;
        }
      }
    }
  }
  &--secondary.ant-btn-default {
    &:not(.ant-btn[disabled]) {
      border: 1px solid transparent;
      background-image:
        linear-gradient(white, white), linear-gradient(110deg, $primaryGradient);
      background-origin: border-box;
      background-clip: padding-box, border-box;
      transition: all 0.3s linear;
      .dtc__aigc__button__text {
        background-image: linear-gradient(110deg, $primaryGradient);
        background-clip: text;
        color: transparent;
      }
      svg,
      path {
        fill: url(#secondary_linear_gradient);
      }
      &:hover {
        background-image:
          linear-gradient(90deg, #f7f9ff 0%, #faf6ff 100%),
          linear-gradient(110deg, $primaryGradientHover);
        svg,
        path {
          fill: url(#secondary_linear_gradient_hover);
        }
        .dtc__aigc__button__text {
          background-image: linear-gradient(110deg, $primaryGradientHover);
        }
      }
    }
  }

  // 只支持 dtc__icon
  .dtc__icon ~ .dtc__aigc__button__text {
    margin-left: 2px;
  }

  &.ant-btn-sm {
    font-size: 12px;
    gap: 2px;
  }
}
