import { Button as Antd<PERSON>utt<PERSON>, ButtonProps } from "antd";
import classNames from "classnames";
import React, { useEffect } from "react";
import { renderToString } from "react-dom/server";

import "./index.less";

interface IButtonProps extends Omit<ButtonProps, "type"> {
  type: "default" | "primary" | "secondary";
}

const GLOBAL_GRADIENT_CLASSNAME = "dtc__aigc__button__global-gradient";
const SECONDARY_LINEAR_GRADIENT_ID = "secondary_linear_gradient";
const SECONDARY_LINEAR_GRADIENT_HOVER_ID = "secondary_linear_gradient_hover";

/**
 * 拷贝自 RC 的 Chat 组件，对样式进行了微调
 */
export default function AigcButton({
  type = "default",
  className,
  children,
  ...rest
}: IButtonProps) {
  useEffect(() => {
    if (!document.querySelector(`.${GLOBAL_GRADIENT_CLASSNAME}`)) {
      const div = document.createElement("div");
      div.style.setProperty("width", "0");
      div.style.setProperty("height", "0");
      div.className = GLOBAL_GRADIENT_CLASSNAME;
      div.innerHTML = renderToString(
        <svg width={0} height={0}>
          <defs>
            <linearGradient
              id={SECONDARY_LINEAR_GRADIENT_ID}
              x1="0.874337"
              y1="3.44009"
              x2="10.967"
              y2="12.0105"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#05A9FE" />
              <stop offset="0.294964" stopColor="#055DFF" />
              <stop offset="0.892987" stopColor="#8B3FFE" />
            </linearGradient>
            <linearGradient
              id={SECONDARY_LINEAR_GRADIENT_HOVER_ID}
              x1="7.75767"
              y1="0.845062"
              x2="13.2784"
              y2="5.53315"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#05A9FE" />
              <stop offset="0.294964" stopColor="#055DFF" />
              <stop offset="0.892987" stopColor="#8B3FFE" />
            </linearGradient>
          </defs>
        </svg>,
      );
      document.body.appendChild(div);
    }
  }, []);

  return (
    <AntdButton
      className={classNames(
        "dtc__aigc__button",
        className,
        `dtc__aigc__button--${type}`,
      )}
      ghost
      {...rest}
    >
      <span className="dtc__aigc__button__text">{children}</span>
    </AntdButton>
  );
}
