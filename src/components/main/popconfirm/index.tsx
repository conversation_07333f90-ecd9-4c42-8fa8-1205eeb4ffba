import {
  Popover,
  PopoverArrow,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/utils/utils";
import React, { ReactNode, useState } from "react";
import { Button, type ButtonProps } from "../button";
import { CircleCloseIcon, CircleInfoIcon } from "../icon";

export type PopconfirmType = "error" | "info";

export interface PopconfirmProps {
  /**
   * 弹出确认框的内容
   */
  content?: ReactNode;
  /**
   * 弹出确认框的标题
   */
  title?: ReactNode;
  /**
   * 确认框的触发器，通常是按钮或文本
   */
  children?: ReactNode;
  /**
   * 确认框的类型，不同类型显示不同图标
   */
  type?: PopconfirmType;
  /**
   * 触发方式，可以是 hover 或 click
   */
  trigger?: "hover" | "click";
  /**
   * 点击确认按钮的回调函数
   */
  onConfirm?: () => void;
  /**
   * 点击取消按钮的回调函数
   */
  onCancel?: () => void;
  /**
   * 确认按钮文字
   */
  confirmText?: ReactNode;
  /**
   * 取消按钮文字
   */
  cancelText?: ReactNode;
  /**
   * 确认按钮的属性
   */
  confirmButtonProps?: ButtonProps;
  /**
   * 取消按钮的属性
   */
  cancelButtonProps?: ButtonProps;
  /**
   * 气泡框类名
   */
  className?: string;
  /**
   * 气泡框样式
   */
  style?: React.CSSProperties;
  /**
   * 手动控制气泡框是否展示（受控模式）
   */
  open?: boolean;
  /**
   * 气泡框显示状态变化时的回调函数（受控模式）
   */
  onOpenChange?: (open: boolean) => void;
  /**
   * 默认气泡框是否打开（非受控模式）
   */
  defaultOpen?: boolean;
  /**
   * 气泡框位置
   */
  placement?: "top" | "bottom" | "left" | "right";
  /**
   * 气泡框的 footer，设置为 null 时，不显示 footer
   */
  footer?: ReactNode;
}

export default function Popconfirm({
  content,
  title,
  children,
  type = "info",
  trigger = "click",
  onConfirm,
  onCancel,
  confirmText = "确认",
  cancelText = "取消",
  confirmButtonProps,
  cancelButtonProps,
  className,
  style,
  open: controlledOpen,
  onOpenChange,
  defaultOpen,
  placement = "bottom",
  footer,
}: PopconfirmProps) {
  const [internalOpen, setInternalOpen] = useState(defaultOpen || false);

  const isControlled = controlledOpen !== undefined;
  const isOpen = isControlled ? controlledOpen : internalOpen;

  const handleOpenChange = (value: boolean) => {
    if (!isControlled) {
      setInternalOpen(value);
    }
    onOpenChange?.(value);
  };

  const handleConfirm = () => {
    onConfirm?.();
    handleOpenChange(false);
  };

  const handleCancel = () => {
    onCancel?.();
    handleOpenChange(false);
  };

  const renderIcon = () => {
    switch (type) {
      case "error":
        return <CircleCloseIcon className="text-xl text-red-1" />;
      case "info":
        return <CircleInfoIcon className="h-5 w-5 text-primary-default" />;
      default:
        return null;
    }
  };

  return (
    <Popover open={isOpen} onOpenChange={handleOpenChange}>
      <PopoverTrigger asChild>
        {trigger === "click" ? (
          <div className="inline-block cursor-pointer">{children}</div>
        ) : (
          <div
            className="inline-block cursor-pointer"
            onMouseEnter={() => handleOpenChange(true)}
            onMouseLeave={() => handleOpenChange(false)}
          >
            {children}
          </div>
        )}
      </PopoverTrigger>
      <PopoverContent
        className={cn(
          "w-auto min-w-[240px] max-w-[280px] rounded-xl border-none p-0 shadow-[0px_5px_22px_0px_rgba(61,68,110,0.20)]",
          className,
        )}
        style={style}
        align="center"
        side={placement}
      >
        <div className="relative p-4">
          <div className="flex items-start gap-3">
            <div className={cn("mt-[1px] flex-shrink-0")}>{renderIcon()}</div>
            <div className="flex-1">
              {title && (
                <div
                  className={cn(
                    "text-sm font-medium leading-[22px]",
                    type === "error" ? "text-red-1" : "text-text-1",
                  )}
                >
                  {title}
                </div>
              )}
              {content && (
                <div className="mt-1 text-xs font-normal leading-5 text-text-2 opacity-80">
                  {content}
                </div>
              )}
            </div>
          </div>
          {footer === undefined ? (
            <div className="mt-3 flex justify-end gap-2">
              <Button
                className={cancelButtonProps?.className}
                variant="outline"
                size="xs"
                onClick={handleCancel}
                {...cancelButtonProps}
              >
                {cancelText}
              </Button>
              <Button
                className={confirmButtonProps?.className}
                variant="primary"
                size="xs"
                onClick={handleConfirm}
                {...confirmButtonProps}
              >
                {confirmText}
              </Button>
            </div>
          ) : (
            footer
          )}
        </div>
        <PopoverArrow className="fill-white" />
      </PopoverContent>
    </Popover>
  );
}
