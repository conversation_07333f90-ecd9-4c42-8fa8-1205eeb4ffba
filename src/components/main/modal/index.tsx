import * as Form from "@radix-ui/react-form";
import React, { ReactNode } from "react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/main/dialog";

import { Button } from "@/components/main/button";
import { modalHeaderType } from "@/types/components";
import { cn } from "@/utils/utils";
import { DialogClose } from "@radix-ui/react-dialog";

type ContentProps = {
  children: ReactNode;
  overflowHidden?: boolean;
  className?: string;
};
type HeaderProps = { children: ReactNode; description: string };
type FooterProps = { children: ReactNode };
type TriggerProps = {
  children: ReactNode;
  asChild?: boolean;
  disable?: boolean;
  className?: string;
};

const Content: React.FC<ContentProps> = ({
  children,
  overflowHidden,
  className,
}) => {
  return (
    <div
      className={cn(
        `flex flex-1 flex-col rounded-md p-6 transition-all duration-300`,
        overflowHidden ? "overflow-hidden" : "",
        className,
      )}
    >
      {children}
    </div>
  );
};

const Trigger: React.FC<TriggerProps> = ({
  children,
  asChild,
  disable,
  className,
}) => {
  return (
    <DialogTrigger
      className={asChild ? "" : cn("w-full", className)}
      hidden={children ? false : true}
      disabled={disable}
      asChild={asChild}
    >
      {children}
    </DialogTrigger>
  );
};

const Header: React.FC<{
  children: ReactNode;
  description?: string | JSX.Element | null;
  clampDescription?: number;
  className?: string;
}> = ({
  children,
  description,
  clampDescription,
  className,
}: modalHeaderType): JSX.Element => {
  return (
    <DialogHeader className={className}>
      <DialogTitle className="line-clamp-1 flex items-center pb-0.5 text-base">
        {children}
      </DialogTitle>
      {description && (
        <DialogDescription
          className={`line-clamp-${clampDescription ?? 2} text-sm`}
        >
          {description}
        </DialogDescription>
      )}
    </DialogHeader>
  );
};

const Footer: React.FC<{
  children?: ReactNode;
  submit?: {
    submitLabel?: string;
    cancelLabel?: string;
    icon?: ReactNode;
    loading?: boolean;
    disabled?: boolean;
    dataTestId?: string;
    onClick?: () => void;
  };
  close?: boolean;
  centered?: boolean;
  className?: string;
}> = ({ children, submit, close, centered, className }) => {
  return (
    <div
      className={cn(
        "flex flex-shrink-0 px-6 py-3",
        centered ? "justify-center" : "flex-row-reverse",
        className,
      )}
    >
      {submit ? (
        <div className="flex w-full items-center justify-end gap-3">
          <DialogClose asChild>
            <Button variant="outline" data-testid="btn-cancel-modal">
              {submit.cancelLabel ?? "取消"}
            </Button>
          </DialogClose>
          <Button
            data-testid={submit.dataTestId}
            type={submit.onClick ? "button" : "submit"}
            onClick={submit.onClick}
            loading={submit.loading}
            disabled={submit.disabled}
          >
            {submit.icon}
            {submit.submitLabel ?? "确定"}
          </Button>
        </div>
      ) : (
        children
      )}
      {close && (
        <DialogClose asChild>
          <Button data-testid="btn-close-modal" variant="outline">
            关闭
          </Button>
        </DialogClose>
      )}
    </div>
  );
};

interface BaseModalProps {
  children:
    | [
        React.ReactElement<ContentProps>,
        React.ReactElement<HeaderProps>?,
        React.ReactElement<TriggerProps>?,
        React.ReactElement<FooterProps>?,
      ]
    | React.ReactElement<ContentProps>;
  open?: boolean;
  setOpen?: (open: boolean) => void;
  className?: string;
  disable?: boolean;
  onEscapeKeyDown?: (e: KeyboardEvent) => void;
  closeButtonClassName?: string;
  onSubmit?: (event: React.FormEvent<HTMLFormElement>) => void;
}

function Modal({
  className,
  open,
  children,
  closeButtonClassName,
  setOpen,
  onEscapeKeyDown,
  onSubmit,
}: BaseModalProps) {
  const headerChild = React.Children.toArray(children).find(
    (child) => (child as React.ReactElement).type === Header,
  );
  const triggerChild = React.Children.toArray(children).find(
    (child) => (child as React.ReactElement).type === Trigger,
  );
  const ContentChild = React.Children.toArray(children).find(
    (child) => (child as React.ReactElement).type === Content,
  );
  const ContentFooter = React.Children.toArray(children).find(
    (child) => (child as React.ReactElement).type === Footer,
  );

  const modalContent = (
    <>
      {headerChild && headerChild}
      {ContentChild}
      {ContentFooter && ContentFooter}
    </>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {triggerChild}
      <DialogContent
        onClick={(e) => e.stopPropagation()}
        onOpenAutoFocus={(event) => event.preventDefault()}
        onEscapeKeyDown={onEscapeKeyDown}
        className={cn(
          "flex max-h-[98dvh] max-w-full flex-1 flex-col",
          className,
        )}
        closeButtonClassName={closeButtonClassName}
      >
        {onSubmit ? (
          <Form.Root
            onSubmit={(event) => {
              event.preventDefault();
              onSubmit(event);
            }}
            className="flex flex-1 flex-col"
          >
            {modalContent}
          </Form.Root>
        ) : (
          modalContent
        )}
      </DialogContent>
    </Dialog>
  );
}

Modal.Content = Content;
Modal.Header = Header;
Modal.Trigger = Trigger;
Modal.Footer = Footer;

export default Modal;
