import { cn } from "@/utils/utils";
import * as React from "react";

export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  password?: boolean;
  size?: "sm" | "md" | "lg";
}

const textAreaSizes = {
  sm: "py-[2px]",
  md: "py-1",
  lg: "py-[6px]",
};

const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  ({ className, password, size = "md", ...props }, ref) => {
    return (
      <div className="h-full w-full">
        <textarea
          data-testid="textarea"
          className={cn(
            "aw-textarea data-[invalid=true]:aw-textarea--error",
            textAreaSizes[size],
            className,
            password ? "password" : "",
          )}
          ref={ref}
          {...props}
          value={props.value as string}
          onChange={props.onChange}
        />
      </div>
    );
  },
);

Textarea.displayName = "Textarea";

export { Textarea };
