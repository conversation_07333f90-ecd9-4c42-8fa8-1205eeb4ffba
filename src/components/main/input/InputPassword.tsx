import ForwardedIconComponent from "@/components/common/genericIconComponent";
import { cn } from "@/utils/utils";
import * as React from "react";

export interface InputProps
  extends Omit<
    React.InputHTMLAttributes<HTMLInputElement>,
    "size" | "onChange"
  > {
  icon?: JSX.Element;
  inputClassName?: string;
  size?: "sm" | "md" | "lg";
  password?: boolean;
  onChange?: (value: string, snapshot?: boolean) => void;
  value?: string;
}

const inputSizes = {
  sm: { height: "h-7", padding: "py-[2px]" },
  md: { height: "h-8", padding: "py-1" },
  lg: { height: "h-9", padding: "py-[6px]" },
};

const InputPassword = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      inputClassName,
      icon,
      type,
      size = "md",
      password,
      disabled,
      onChange,
      value,
      ...props
    },
    ref,
  ) => {
    const sizes = inputSizes[size];
    const [pwdVisible, setPwdVisible] = React.useState(false);

    React.useEffect(() => {
      if (disabled && value && onChange && value !== "") {
        onChange("", true);
      }
    }, [disabled]);

    if (icon) {
      return (
        <label className={cn("relative block w-full", sizes.height, className)}>
          <span className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground">
            {icon}
          </span>
          <input
            autoComplete="off"
            data-testid=""
            type={password && !pwdVisible ? "password" : "text"}
            value={value}
            className={cn(
              "aw-input data-[invalid=true]:aw-input--error pl-9 pr-3",
              password && !pwdVisible && value !== ""
                ? "text-clip password"
                : "",
              sizes.padding,
              inputClassName,
            )}
            ref={ref}
            {...props}
            onChange={(e) => {
              if (onChange) onChange(e.target.value);
            }}
          />
        </label>
      );
    }

    return (
      <label className={cn("relative block w-full", sizes.height, className)}>
        <input
          autoComplete="off"
          data-testid=""
          type={password && !pwdVisible ? "password" : "text"}
          value={value}
          className={cn(
            "aw-input data-[invalid=true]:aw-input--error",
            password && !pwdVisible && value !== "" ? "text-clip password" : "",
            sizes.padding,
            inputClassName,
          )}
          ref={ref}
          {...props}
          onChange={(e) => {
            if (onChange) onChange(e.target.value);
          }}
        />
        {password && (
          <button
            type="button"
            tabIndex={-1}
            className={cn("input-component-false-button mb-px mr-3 p-0")}
            onClick={(event) => {
              event.preventDefault();
              setPwdVisible(!pwdVisible);
            }}
          >
            {pwdVisible ? (
              <ForwardedIconComponent
                name="EyeAiWorkflowIcon"
                className="icon-size text-sm text-text-2"
              />
            ) : (
              <ForwardedIconComponent
                name="NoEyeAiWorkflowIcon"
                className="icon-size text-sm text-text-2"
              />
            )}
          </button>
        )}
      </label>
    );
  },
);

InputPassword.displayName = "InputPassword";

export { InputPassword };
