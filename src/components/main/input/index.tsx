import { cn } from "@/utils/utils";
import * as React from "react";

export interface InputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size"> {
  icon?: JSX.Element;
  inputClassName?: string;
  size?: "sm" | "md" | "lg";
}

const inputSizes = {
  sm: { height: "h-7", padding: "py-[2px]" },
  md: { height: "h-8", padding: "py-1" },
  lg: { height: "h-9", padding: "py-[6px]" },
};

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, inputClassName, icon, type, size = "md", ...props }, ref) => {
    const sizes = inputSizes[size];

    if (icon) {
      return (
        <label className={cn("relative block w-full", sizes.height, className)}>
          <span className="absolute left-2 top-1/2 h-4 w-4 -translate-y-1/2 transform text-muted-foreground">
            {icon}
          </span>
          <input
            autoComplete="off"
            data-testid=""
            type={type}
            className={cn(
              "aw-input data-[invalid=true]:aw-input--error pl-9 pr-3",
              sizes.padding,
              inputClassName,
            )}
            ref={ref}
            {...props}
          />
        </label>
      );
    }

    return (
      <label className={cn("relative block w-full", sizes.height, className)}>
        <input
          data-testid=""
          type={type}
          className={cn(
            "aw-input data-[invalid=true]:aw-input--error",
            sizes.padding,
            inputClassName,
          )}
          ref={ref}
          {...props}
        />
      </label>
    );
  },
);

Input.displayName = "Input";

export { Input };
