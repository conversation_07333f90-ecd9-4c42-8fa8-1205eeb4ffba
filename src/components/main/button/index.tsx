import ForwardedIconComponent from "@/components/common/genericIconComponent";
import { cn } from "@/utils/utils";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import * as React from "react";

const buttonVariants = cva(
  "rounded-lg inline-flex items-center justify-center gap font-normal whitespace-nowrap border border-transparent transition",
  {
    variants: {
      variant: {
        destructive:
          "bg-red-2 text-red-1 [&:not(:disabled)]:active:border-red-1 [&:not(:disabled)]:active:shadow-[0px_0px_0px_2px_rgba(255,95,76,0.1)] [&:not(:disabled)]:hover:bg-red-4",
        outline:
          "!border-border-1 bg-white hover:bg-bg-light-4 disabled:bg-bg-light-4 [&:not(:disabled)]:active:!border-primary-default [&:not(:disabled)]:active:shadow-[0px_0px_0px_2px_rgba(5,56,255,0.1)]",
        primary:
          "bg-bg-primary-1 text-primary-default [&:not(:disabled)]:hover:bg-bg-primary-3 [&:not(:disabled)]:active:border-primary-default [&:not(:disabled)]:active:shadow-[0px_0px_0px_2px_rgba(5,56,255,0.1)]",
        link: "text-primary-default !p-0 !h-auto border-0",
        ghost:
          "bg-transparent hover:border-border-1 [&:not(:disabled)]:active:!border-primary-default [&:not(:disabled)]:active:shadow-[0px_0px_0px_2px_rgba(5,56,255,0.1)]",
        nodeToolbar:
          "text-foreground hover:bg-accent hover:text-accent-foreground disabled:!bg-transparent",
      },
      size: {
        md: "h-8 py-1 px-4 text-sm leading-[22px] gap-2 [&_svg]:size-4",
        sm: "h-7 py-[3px] px-3 text-xs leading-[20px] gap-1 [&_svg]:size-4",
        xs: "h-6 py-[1px] px-2 text-xs leading-[20px] gap-0.5 [&_svg]:size-[14px]",
        iconMd: "h-8 p-[7px] leading-4",
        iconSm: "h-7 p-[5px] leading-4",
        iconXs: "h-6 p-[4px] leading-[14px] [&_svg]:size-[14px]",
      },
    },
    defaultVariants: {
      variant: "primary",
      size: "md",
    },
  },
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  unstyled?: boolean;
  ignoreTitleCase?: boolean;
}

function toTitleCase(text: string) {
  return text
    ?.split(" ")
    ?.map(
      (word) => word?.charAt(0)?.toUpperCase() + word?.slice(1)?.toLowerCase(),
    )
    ?.join(" ");
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      unstyled,
      size,
      loading,
      type,
      disabled,
      asChild = false,
      children,
      ignoreTitleCase = false,
      ...props
    },
    ref,
  ) => {
    const Comp = asChild ? Slot : "button";
    const diabledClass =
      loading || disabled ? "cursor-not-allowed opacity-40" : "";
    let newChildren = children;

    if (typeof children === "string") {
      newChildren = ignoreTitleCase ? children : toTitleCase(children);
    }

    return (
      <>
        <Comp
          className={
            !unstyled
              ? buttonVariants({
                  variant,
                  size,
                  className: cn(className, diabledClass),
                })
              : cn(className)
          }
          disabled={loading || disabled}
          ref={ref}
          {...(asChild ? {} : { type: type || "button" })}
          {...props}
        >
          {loading ? (
            <ForwardedIconComponent
              name={"Loader2"}
              className={"animate-spin"}
            />
          ) : null}
          {newChildren}
        </Comp>
      </>
    );
  },
);

Button.displayName = "Button";

export { Button, buttonVariants };
