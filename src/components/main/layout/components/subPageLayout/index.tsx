import { QuestionCircleOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";

import { But<PERSON> } from "@/components/main/button";
import { LeftArrowIcon } from "@/components/main/icon";
import { useCustomNavigate } from "@/customization/hooks/use-custom-navigate";
import { cn } from "@/utils/utils";

interface SubPageLayoutProps {
  title: string;
  backPath: string;
  children: React.ReactNode;
  navRightContent?: React.ReactNode;
  tooltip?: string;
  wrapperClassName?: string;
}

export default function SubPageLayout({
  title,
  backPath,
  children,
  navRightContent,
  tooltip,
  wrapperClassName,
}: SubPageLayoutProps) {
  const navigate = useCustomNavigate();

  return (
    <div className="flex h-full flex-col gap-4">
      <div className="flex">
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            size="iconSm"
            onClick={() => navigate(backPath)}
          >
            <LeftArrowIcon className="text-text-2" />
          </Button>
          <div className="h-4 border-l-[1px] border-border-1"></div>
          <h1 className="text-lg font-semibold">{title}</h1>
          {tooltip && (
            <Tooltip title={tooltip} placement="right">
              <QuestionCircleOutlined />
            </Tooltip>
          )}
        </div>
        <div className="flex-1">{navRightContent}</div>
      </div>
      <div className={cn("min-h-0 flex-1", wrapperClassName)}>{children}</div>
    </div>
  );
}
