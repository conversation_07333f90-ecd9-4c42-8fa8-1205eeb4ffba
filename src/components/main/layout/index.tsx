import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { Outlet } from "react-router-dom";
import Sider from "./components/sider";

export default function Layout() {
  return (
    <div className="flex h-full gap-4 p-4">
      <SidebarProvider
        className="sidebar w-auto"
        width="240px"
        style={{ "--sidebar-width-icon": "5rem" } as React.CSSProperties}
      >
        <Sider />
      </SidebarProvider>
      <SidebarInset className="h-full min-h-full w-full">
        <Outlet />
      </SidebarInset>
    </div>
  );
}
