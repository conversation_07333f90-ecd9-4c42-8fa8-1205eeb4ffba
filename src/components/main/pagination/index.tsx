import React, { useEffect, useMemo, useState } from "react";

import { cn } from "@/utils/utils";
import { Button } from "../button";
import { LeftArrowIcon, RightArrowIcon } from "../icon";

interface PaginationProps {
  total?: number;
  pageSize?: number;
  current?: number;
  onChange?: (page: number, pageSize?: number) => void;
  className?: string;
}

export default function Pagination({
  total = 0,
  pageSize = 20,
  current = 1,
  onChange,
  className,
}: PaginationProps) {
  const totalPages = useMemo(
    () => Math.max(1, Math.ceil(total / pageSize)),
    [total, pageSize],
  );

  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages || page === current) return;

    onChange?.(page, pageSize);
  };

  // 生成页码数组
  const generatePageNumbers = () => {
    const pageNumbers: (number | string)[] = [];

    // 最多显示的页码数
    const maxVisiblePages = 5;

    if (totalPages <= maxVisiblePages) {
      // 总页数小于等于最大显示数时，全部显示
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      // 总页数大于最大显示数，需要显示省略号
      if (current <= 3) {
        // 当前页靠近开始
        for (let i = 1; i <= 4; i++) {
          pageNumbers.push(i);
        }
        pageNumbers.push("...");
        pageNumbers.push(totalPages);
      } else if (current >= totalPages - 2) {
        // 当前页靠近结束
        pageNumbers.push(1);
        pageNumbers.push("...");
        for (let i = totalPages - 3; i <= totalPages; i++) {
          pageNumbers.push(i);
        }
      } else {
        // 当前页在中间
        pageNumbers.push(1);
        pageNumbers.push("...");
        pageNumbers.push(current - 1);
        pageNumbers.push(current);
        pageNumbers.push(current + 1);
        pageNumbers.push("...");
        pageNumbers.push(totalPages);
      }
    }

    return pageNumbers;
  };

  return (
    <div
      className={cn(
        "flex h-full items-center justify-end gap-2 text-sm text-gray-500",
        className,
      )}
    >
      <div className="flex items-center text-sm font-normal">
        共<span className="mx-1 text-primary-default">{total}</span>
        条数据，每页
        <span className="mx-1">{pageSize}</span>条
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="iconXs"
          onClick={() => handlePageChange(current - 1)}
          disabled={current === 1}
        >
          <LeftArrowIcon className="text-text-2" />
        </Button>
        <div className="flex items-center gap-2 text-text-1">
          {generatePageNumbers().map((pageNumber, index) => (
            <React.Fragment key={index}>
              {pageNumber === "..." ? (
                <span className="flex h-8 w-8 items-center justify-center">
                  ...
                </span>
              ) : (
                <Button
                  variant="ghost"
                  size="iconXs"
                  className={cn(
                    "min-w-6 px-0",
                    pageNumber === current &&
                      "!border-primary-default text-primary-default",
                  )}
                  onClick={() => handlePageChange(Number(pageNumber))}
                >
                  {pageNumber}
                </Button>
              )}
            </React.Fragment>
          ))}
        </div>
        <Button
          variant="ghost"
          size="iconXs"
          onClick={() => handlePageChange(current + 1)}
          disabled={current === totalPages}
        >
          <RightArrowIcon className="text-text-2" />
        </Button>
      </div>
    </div>
  );
}
