import React from "react";

import { cn } from "@/utils/utils";
import { cva, type VariantProps } from "class-variance-authority";

enum BadgeVariant {
  DEFAULT = "default",
  PROCESSING = "processing",
  SUCCESS = "success",
  ERROR = "error",
  WARN = "warn",
  WAIT_SYNC = "wait_sync",
}

const badgeVariants = cva(
  "inline-flex items-center rounded-xl font-normal transition-colors gap-1",
  {
    variants: {
      variant: {
        [BadgeVariant.DEFAULT]: "bg-bg-light-5 text-text-1",
        [BadgeVariant.PROCESSING]: "bg-bg-primary-1 text-primary-default",
        [BadgeVariant.SUCCESS]: "bg-[rgba(17,215,178,0.10)] text-[#11D7B2]",
        [BadgeVariant.ERROR]: "bg-red-3 text-red-1",
        [BadgeVariant.WARN]: "bg-[rgba(251,179,16,0.15)] text-[#FBB310]",
        // TODO: 添加 WAIT_SYNC 样式
        [BadgeVariant.WAIT_SYNC]: "bg-red-3 text-red-1",
      },
      size: {
        md: "h-5 px-2 text-xs leading-[20px] rounded-xl",
      },
    },
    defaultVariants: {
      variant: BadgeVariant.DEFAULT,
      size: "md",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  children: React.ReactNode;
  showDot?: boolean;
}

function Badge({
  className,
  variant,
  size,
  children,
  showDot,
  ...props
}: BadgeProps) {
  const getDotColor = () => {
    switch (variant) {
      case BadgeVariant.SUCCESS:
        return "bg-[#11D7B2]";
      case BadgeVariant.ERROR:
        return "bg-red-1";
      case BadgeVariant.PROCESSING:
        return "bg-primary-default";
      case BadgeVariant.DEFAULT:
        return "bg-text-1";
      case BadgeVariant.WARN:
        return "bg-[#FBB310]";
      case BadgeVariant.WAIT_SYNC:
        // TODO: 添加 WAIT_SYNC 样式
        return "bg-[#FBB310]";
      default:
        return "";
    }
  };

  return (
    <div className={cn(badgeVariants({ variant, size }), className)} {...props}>
      {showDot && (
        <span
          className={cn(
            "inline-block h-[6px] w-[6px] rounded-full",
            getDotColor(),
          )}
        ></span>
      )}
      <span>{children}</span>
    </div>
  );
}

export { Badge, BadgeVariant, badgeVariants };
