export const buildColorByName = (
  {
    startColor,
    midColor,
    endColor,
  }: { startColor: string; midColor: string; endColor: string },
  percentage: number,
) => {
  const hslColorInfo = (hslInfo: string) => {
    const hue = parseInt(hslInfo.split(" ")[0]);
    const saturation = parseInt(hslInfo.split(" ")[1].replace("%", ""));
    const lightness = parseInt(hslInfo.split(" ")[2].replace("%", ""));
    return { hue, saturation, lightness };
  };

  const calculateColor = (
    start: number,
    mid: number,
    end: number,
    percentage: number,
  ) => {
    if (percentage <= 50) {
      const ratio = percentage / 50;
      return start + (mid - start) * ratio;
    } else {
      const ratio = (percentage - 50) / 50;
      return mid + (end - mid) * ratio;
    }
  };
  const startColorInfo = hslColorInfo(startColor);
  const midColorInfo = hslColorInfo(midColor);
  const endColorInfo = hslColorInfo(endColor);

  const hue = calculateColor(
    startColorInfo.hue,
    midColorInfo.hue,
    endColorInfo.hue,
    percentage,
  );
  const saturation = calculateColor(
    startColorInfo.saturation,
    midColorInfo.saturation,
    endColorInfo.saturation,
    percentage,
  );
  const lightness = calculateColor(
    startColorInfo.lightness,
    midColorInfo.lightness,
    endColorInfo.lightness,
    percentage,
  );

  return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
};
