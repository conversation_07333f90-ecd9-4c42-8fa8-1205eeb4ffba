import { useTranslate } from "@/locales";
import { CustomCellEditorProps } from "ag-grid-react";
import InputComponent from "../../../inputComponent";

export default function TableDropdownCellEditor({
  value,
  values,
  onValueChange,
  colDef,
}: CustomCellEditorProps & { values: string[] }) {
  const { t } = useTranslate("workFlow");
  return (
    <div className="flex h-full items-center px-2">
      <InputComponent
        setSelectedOption={(value) => onValueChange(value)}
        value={value}
        options={values}
        password={false}
        placeholder={t("common.multiselectPlaceholder")}
        id="apply-to-fields"
      />
    </div>
  );
}
