import { ForwardedIconComponent } from "@/components/common/genericIconComponent";
import { AuthContext } from "@/contexts/authContext";
import { BASE_URL_API } from "@/customization/config-constants";
import { useContext } from "react";

export function ProfileIcon() {
  const { userData } = useContext(AuthContext);

  const profileImageUrl = `${BASE_URL_API}files/profile_pictures/${
    userData?.profile_image ?? "Space/046-rocket.svg"
  }`;

  if (!userData?.profile_image) {
    return <ForwardedIconComponent name="User" />;
  }

  return (
    <img
      src={profileImageUrl}
      className="h-7 w-7 shrink-0 focus-visible:outline-0"
    />
  );
}
