/**
 * enum for the different types of nodes
 * @enum
 */
export enum TypeModal {
  TEXT = 1,
  PROMPT = 2,
}

export enum BuildStatus {
  BUILDING = "BUILDING",
  TO_BUILD = "TO_BUILD",
  BUILT = "BUILT",
  INACTIVE = "INACTIVE",
  ERROR = "ERROR",
}

export enum InputOutput {
  INPUT = "input",
  OUTPUT = "output",
}

export enum IOInputTypes {
  TEXT = "TextInput",
  FILE_LOADER = "FileLoader",
  KEYPAIR = "KeyPairInput",
  JSON = "JsonInput",
  STRING_LIST = "StringListInput",
}

export enum IOOutputTypes {
  TEXT = "TextOutput",
  PDF = "PDFOutput",
  CSV = "CSVOutput",
  IMAGE = "ImageOutput",
  JSON = "JsonOutput",
  KEY_PAIR = "KeyPairOutput",
  STRING_LIST = "StringListOutput",
  DATA = "DataOutput",
}

export enum EventDeliveryType {
  STREAMING = "streaming",
  POLLING = "polling",
  DIRECT = "direct",
}

export enum Role {
  ROOT = "root",
  OWNER = "owner",
  ADMIN = "admin",
  MEMBER = "member",
}

export enum PermissionScope {
  /** 只有我 */
  OWNER_ONLY = "owner_only",
  /** 所在部门 */
  ORGANIZATION = "organization",
  /** 指定成员 */
  MEMBERS = "members",
  /** 所有成员 */
  TENANT_ALL = "tenant_all",
}

export enum PermissionType {
  /** 可编辑, 对应 member 角色 */
  EDITABLE = "member",
  /** 可管理, 对应 admin 角色 */
  MANAGEABLE = "admin",
}

export enum Member {
  USER = "user",
  DEPARTMENT = "department",
}

/** ------ 工作流 ------- */

export enum NodeType {
  START = "start",
  END = "end",
  KNOWLEDGE = "knowledge",
  LLM = "llm",
  CONDITION = "condition",
  CUSTOM = "custom",
}

export enum WorkflowStatus {
  DRAFT = "draft",
  SAVED = "saved",
  PUBLISHED = "published",
}
