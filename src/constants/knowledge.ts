import { BadgeVariant } from "@/components/main/badge";

export enum RunningStatus {
  UNINDEX = "0", // 未索引
  INDEXING = "1", // 索引中
  CANCEL = "2", // 取消
  AVAILABLE = "3", // 可用
  FAIL = "4", // 索引失败
  WAIT_SYNC = "5", //  等待同步
}

export const runningStatusMap = {
  [RunningStatus.UNINDEX]: {
    variant: BadgeVariant.DEFAULT,
    text: "未索引",
  },
  [RunningStatus.INDEXING]: {
    variant: BadgeVariant.PROCESSING,
    text: "索引中",
  },
  [RunningStatus.CANCEL]: {
    variant: BadgeVariant.DEFAULT,
    text: "取消",
  },
  [RunningStatus.AVAILABLE]: {
    variant: BadgeVariant.SUCCESS,
    text: "可用",
  },
  [RunningStatus.FAIL]: {
    variant: BadgeVariant.ERROR,
    text: "索引失败",
  },
  [RunningStatus.WAIT_SYNC]: {
    variant: BadgeVariant.WAIT_SYNC,
    text: "待同步",
  },
};
