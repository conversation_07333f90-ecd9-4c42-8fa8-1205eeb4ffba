import {
  LLMNodeIcon,
  ReplyNodeIcon,
  StartNodeIcon,
} from "@/components/main/icon";
import { NodeType } from "@/constants/enums";
import {
  NodeCategory,
  NodeCategoryConfig,
  NodeSelectItem,
} from "@/types/workflow/node";

/**
 * 可选择的节点列表配置
 */
export const NODE_SELECT_ITEMS: NodeSelectItem[] = [
  {
    id: "llm",
    label: "LLM",
    description: "大语言模型节点，用于文本生成和对话",
    type: NodeType.LLM,
    category: NodeCategory.NONE,
    enabled: true,
  },
  {
    id: "reply",
    label: "回复",
    description: "输出最终回复内容",
    type: NodeType.END,
    category: NodeCategory.NONE,
    enabled: true,
  },
  // TODO: 暂时隐藏未支持的节点
  // {
  //   id: "knowledge",
  //   label: "知识库检索",
  //   description: "从知识库中检索相关信息",
  //   type: NodeType.KNOWLEDGE,
  //   category: NodeCategory.NONE,
  //   enabled: true,
  // },
  // {
  //   id: "condition",
  //   label: "条件分支",
  //   description: "根据条件进行分支判断",
  //   type: NodeType.CONDITION,
  //   category: NodeCategory.LOGIC,
  //   enabled: true,
  // },
  // {
  //   id: "custom",
  //   label: "自定义组件",
  //   description: "自定义功能组件",
  //   type: NodeType.CUSTOM,
  //   category: NodeCategory.TOOLS,
  //   enabled: true,
  // },
];

/**
 * 节点分类配置
 */
export const NODE_CATEGORIES: NodeCategoryConfig[] = [
  {
    key: NodeCategory.NONE,
    label: "搜索节点",
    showTitle: false, // 无分类节点不显示标题
    nodes: NODE_SELECT_ITEMS.filter(
      (item) => item.category === NodeCategory.NONE,
    ),
  },
  // TODO: 暂时隐藏未支持的分类
  // {
  //   key: NodeCategory.LOGIC,
  //   label: "逻辑判断",
  //   showTitle: true,
  //   nodes: NODE_SELECT_ITEMS.filter(
  //     (item) => item.category === NodeCategory.LOGIC,
  //   ),
  // },
  // {
  //   key: NodeCategory.TOOLS,
  //   label: "工具",
  //   showTitle: true,
  //   nodes: NODE_SELECT_ITEMS.filter(
  //     (item) => item.category === NodeCategory.TOOLS,
  //   ),
  // },
];

export const NODE_ICON_MAP = {
  [NodeType.START]: {
    component: StartNodeIcon,
    iconColor: "#F78801",
    bgColor: "#FFF1E3",
  },
  [NodeType.LLM]: {
    component: LLMNodeIcon,
    iconColor: "#0071E9",
    bgColor: "#E2E7FF",
  },
  [NodeType.END]: {
    component: ReplyNodeIcon,
    iconColor: "#29845A",
    bgColor: "#E3FFED",
  },
};
