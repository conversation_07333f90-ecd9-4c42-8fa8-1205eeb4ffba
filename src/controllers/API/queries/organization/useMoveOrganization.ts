import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id: string;
  org_id: string;
  new_parent_id: string;
}

export const useMoveOrganization: useMutationFunctionType<
  undefined,
  RequestParams,
  string
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const moveOrganizationFn = async (
    payload: RequestParams,
  ): Promise<string> => {
    const { tenant_id } = payload;
    const url = buildQueryStringUrl(
      `${getURL("MOVE_ORGANIZATION", { org_id: payload.org_id }, false, true)}/move`,
      { tenant_id },
    );
    const res = await api.put<ResponseType<string>>(url, {
      new_parent_id: payload.new_parent_id,
    });
    return res.data?.data;
  };

  const mutation = mutate(["useMoveOrganization"], moveOrganizationFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationTree"],
      });
    },
  });

  return mutation;
};
