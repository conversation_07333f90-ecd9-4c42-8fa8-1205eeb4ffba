import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { Organization } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id: string;
  org_id: string;
  name?: string;
  org_type?: "department";
  description?: string;
  sort_order?: number;
  status?: string;
}

export const useUpdateOrganization: useMutationFunctionType<
  undefined,
  RequestParams,
  Organization
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateOrganizationFn = async (
    payload: RequestParams,
  ): Promise<Organization> => {
    const { tenant_id, org_id, ...rest } = payload;
    const url = buildQueryStringUrl(
      getURL("UPDATE_ORGANIZATION", { org_id }, false, true),
      { tenant_id },
    );
    const res = await api.put<ResponseType<Organization>>(url, rest);
    return res.data?.data;
  };

  const mutation = mutate(["useUpdateOrganization"], updateOrganizationFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetOrganizationTree"],
      });
    },
  });

  return mutation;
};
