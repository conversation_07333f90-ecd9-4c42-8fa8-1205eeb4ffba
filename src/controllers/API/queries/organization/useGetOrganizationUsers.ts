import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { OrganizationUserPageData } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id?: string;
  org_id?: string;
  page?: number;
  size?: number;
  search?: string;
  order_by?: string;
  desc?: boolean;
}

type ReturnData = OrganizationUserPageData;

export const useGetOrganizationUsers: useQueryFunctionType<
  RequestParams,
  ReturnData | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getOrganUsersFn = async (
    params: RequestParams,
  ): Promise<ReturnData | undefined> => {
    const url = buildQueryStringUrl(
      `${getURL("GET_ORGANIZATION_USERS", {}, false, true)}`,
      params,
    );
    const res = await api.get<ResponseType<ReturnData>>(url);
    return res.data?.data;
  };

  const queryResult = query(
    [
      "useGetOrganizationUsers",
      {
        org_id: params.org_id,
        page: params.page,
        size: params.size,
        search: params.search,
        order_by: params.order_by,
        desc: params.desc,
      },
    ],
    () => getOrganUsersFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
