import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { Organization } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id?: string;
  root_id?: string;
}

type ReturnData = Organization[];

export const useGetOrganizationTree: useQueryFunctionType<
  RequestParams,
  ReturnData | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getOrganTreeFn = async (
    params: RequestParams,
  ): Promise<ReturnData | undefined> => {
    const url = buildQueryStringUrl(
      `${getURL("GET_ORGANIZATION_TREE", {}, false, true)}`,
      params,
    );
    const res = await api.get<ResponseType<ReturnData>>(url);
    return res.data?.data;
  };

  const queryResult = query(
    [
      "useGetOrganizationTree",
      {
        tenant_id: params.tenant_id,
        root_id: params.root_id,
      },
    ],
    () => getOrganTreeFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
