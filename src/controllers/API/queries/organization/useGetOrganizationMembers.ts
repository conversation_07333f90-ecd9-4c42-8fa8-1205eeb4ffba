import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { OrganizationMember } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  org_id?: string;
}

type ReturnData = OrganizationMember[];

export const useGetOrganizationMembers: useQueryFunctionType<
  RequestParams,
  ReturnData | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getOrganMembersFn = async (
    params: RequestParams,
  ): Promise<ReturnData | undefined> => {
    const url = buildQueryStringUrl(
      getURL("GET_ORGANIZATION_MEMBERS", {}, false, true),
      params,
    );
    const res = await api.get<ResponseType<ReturnData>>(url);
    return res.data?.data;
  };

  const queryResult = query(
    [
      "useGetOrganizationMembers",
      {
        org_id: params.org_id,
      },
    ],
    () => getOrganMembersFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
