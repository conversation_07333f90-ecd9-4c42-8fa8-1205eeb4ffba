import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { OrganizationUser } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

export const useUpdateUser: useMutationFunctionType<
  undefined,
  Partial<OrganizationUser>,
  OrganizationUser | null
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateUserFn = async (
    payload: Partial<OrganizationUser>,
  ): Promise<OrganizationUser | null> => {
    if (!payload.id) return null;
    const url = buildQueryStringUrl(
      getURL("UPDATE_USER", { user_id: payload.id }, false, true),
      { tenant_id: payload.tenant_id },
    );
    const res = await api.put<ResponseType<OrganizationUser>>(url, payload);
    return res.data?.data;
  };

  const mutation = mutate(["useUpdateUser"], updateUserFn, {
    ...options,
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ["useGetOrganizationUsers"],
      });
    },
  });

  return mutation;
};
