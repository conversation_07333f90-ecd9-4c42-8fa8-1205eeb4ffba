import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { OrganizationUser } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id: string;
  user_id: string;
  new_password: string;
}

export const useUpdatePassword: useMutationFunctionType<
  undefined,
  RequestParams,
  OrganizationUser
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updatePasswordFn = async (
    payload: RequestParams,
  ): Promise<OrganizationUser> => {
    const url = buildQueryStringUrl(
      `${getURL("UPDATE_PASSWORD", { user_id: payload.user_id }, false, true)}/password`,
      { tenant_id: payload.tenant_id },
    );
    const res = await api.put<ResponseType<OrganizationUser>>(url, {
      new_password: payload.new_password,
    });
    return res.data?.data;
  };

  const mutation = mutate(["useUpdatePassword"], updatePasswordFn, {
    ...options,
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ["useGetOrganizationUsers"],
      });
    },
  });

  return mutation;
};
