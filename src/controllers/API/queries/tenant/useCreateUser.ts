import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { OrganizationUser, UserFormData } from "@/types/organization";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams extends UserFormData {
  tenant_id: string;
}

export const useCreateUser: useMutationFunctionType<
  undefined,
  RequestParams,
  OrganizationUser
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const createUserFn = async (
    payload: RequestParams,
  ): Promise<OrganizationUser> => {
    const { tenant_id, ...rest } = payload;
    const url = buildQueryStringUrl(getURL("CREATE_USER", {}, false, true), {
      tenant_id,
    });
    const res = await api.post<ResponseType<OrganizationUser>>(url, rest);
    return res.data?.data;
  };

  const mutation = mutate(["useCreateUser"], createUserFn, {
    ...options,
    onSettled: () => {
      queryClient.invalidateQueries({
        queryKey: ["useGetOrganizationUsers"],
      });
    },
  });

  return mutation;
};
