import { useMutationFunctionType } from "@/types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface CreateShareTokenParams {
  app_id: string;
  regenerate?: boolean;
}

type CreateShareTokenReturnType = string;

export const useCreateShareToken: useMutationFunctionType<
  undefined,
  CreateShareTokenParams,
  CreateShareTokenReturnType
> = (options) => {
  const { mutate } = UseRequestProcessor();

  const createShareTokenFn = async (
    payload: CreateShareTokenParams,
  ): Promise<CreateShareTokenReturnType> => {
    const url = `${getURL("CREATE_SHARE_TOKEN", { app_id: payload.app_id })}/share`;
    const { data } = await api.post<{ share_token: string }>(
      url,
      payload.regenerate,
    );
    const shareToken = data?.share_token || "";

    return shareToken;
  };

  const mutation = mutate(["useCreateShareToken"], createShareTokenFn, {
    ...options,
  });

  return mutation;
};
