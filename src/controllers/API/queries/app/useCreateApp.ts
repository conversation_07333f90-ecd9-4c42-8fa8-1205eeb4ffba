import { useMutationFunctionType } from "@/types/api";
import type { App, CreateAppParams } from "@/types/app";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

export const useCreateApp: useMutationFunctionType<
  undefined,
  CreateAppParams,
  App
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const createAppFn = async (payload: CreateAppParams): Promise<App> => {
    const url = getURL("CREATE_APP");
    const { data } = await api.post<App>(url, payload);
    return data;
  };

  const mutation = mutate(["useCreateApp"], createAppFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetApps"],
      });
    },
  });

  return mutation;
};
