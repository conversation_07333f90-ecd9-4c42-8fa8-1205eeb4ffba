import { useRef } from "react";

import { sse } from "../../api";
import { getURL } from "../../helpers/constants";

export type ActionType = "message" | "message_end";

export interface MessageData {
  event: ActionType;
  content: string;
  conversation_id: string;
  created_at: string;
}

function parseJsonFromDataString(str) {
  try {
    const match = str.match(/^data:\s*(\{.*\})\n$/);
    if (!match) return null;

    const jsonStr = match[1];
    return JSON.parse(jsonStr);
  } catch (err) {
    console.error("JSON解析失败：", err);
    return null;
  }
}

export function useAIChat() {
  const abortController = useRef<AbortController | undefined>(undefined);

  function mutate(
    params: Record<string, any>,
    listeners: {
      onopen: () => void;
      onmessage: (data: MessageData) => void;
      onclose: (data?: MessageData) => void;
    },
  ) {
    const url = getURL("APP_CHAT", { appId: params.appId });
    abortController.current = new AbortController();
    try {
      sse(`${url}/chat`, params, {
        signal: abortController.current.signal,
        openWhenHidden: true,
        async onopen() {
          listeners.onopen?.();
        },
        onmessage(ev) {
          const data: MessageData = parseJsonFromDataString(ev.data);
          if (!data) return;
          if (data.event === "message_end") {
            listeners.onclose?.(data);
            return;
          }
          listeners.onmessage?.(data);
        },
        onclose() {
          listeners.onclose?.();
        },
      });
    } catch {}
  }

  function abort() {
    abortController.current?.abort();
  }

  return { mutate, abort };
}
