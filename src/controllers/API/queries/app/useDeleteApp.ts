import { useMutationFunctionType } from "@/types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface DeleteAppParams {
  app_id: string;
}

type DeleteAppReturnType = null;

export const useDeleteApp: useMutationFunctionType<
  undefined,
  DeleteAppParams,
  DeleteAppReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteAppFn = async (
    payload: DeleteAppParams,
  ): Promise<DeleteAppReturnType> => {
    const url = getURL("DELETE_APP", { app_id: payload.app_id });
    const { data } = await api.delete<DeleteAppReturnType>(url);
    return data;
  };

  const mutation = mutate(["useDeleteApp"], deleteAppFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetApps"],
      });
    },
  });

  return mutation;
};
