import { useQueryFunctionType } from "@/types/api";
import { App } from "@/types/app";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetAppDetailParams {
  appId: string;
}

type GetAppDetailReturnType = App;

export const useGetAppDetail: useQueryFunctionType<
  GetAppDetailParams,
  GetAppDetailReturnType | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getAppDetailFn = async (
    params: GetAppDetailParams,
  ): Promise<GetAppDetailReturnType | undefined> => {
    const { data } = await api.get<GetAppDetailReturnType>(
      `${getURL("GET_APP_DETAIL", { appId: params.appId })}`,
    );
    return data;
  };

  const queryResult = query(
    [
      "useGetAppDetail",
      {
        appId: params.appId,
      },
    ],
    () => getAppDetailFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
