import { useQueryFunctionType } from "@/types/api";
import { ConversationDetail } from "@/types/app";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetConversationDetailParams {
  appId: string;
  conversationId: string;
}

export const useGetConversationDetail: useQueryFunctionType<
  GetConversationDetailParams,
  ConversationDetail | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getConversationDetailFn = async (
    params: GetConversationDetailParams,
  ): Promise<ConversationDetail | undefined> => {
    if (!params.appId || !params.conversationId) return undefined;
    const { data } = await api.get<ConversationDetail>(
      `${getURL("GET_CONVERSATION_DETAIL", { appId: params.appId })}/conversations/${params.conversationId}`,
    );
    return data;
  };

  const queryResult = query(
    [
      "useGetConversationDetail",
      {
        appId: params.appId,
        conversationId: params.conversationId,
      },
    ],
    () => getConversationDetailFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
