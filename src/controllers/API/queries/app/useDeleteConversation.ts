import { useMutationFunctionType } from "@/types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface DeleteConversationParams {
  appId: string;
  conversationId: string;
}

type DeleteConversationReturnType = null;

export const useDeleteConversation: useMutationFunctionType<
  undefined,
  DeleteConversationParams,
  DeleteConversationReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteConversationFn = async (
    payload: DeleteConversationParams,
  ): Promise<DeleteConversationReturnType> => {
    const url = `${getURL("DELETE_CONVERSATION", { appId: payload.appId })}/conversations/${payload.conversationId}`;
    const { data } = await api.delete<DeleteConversationReturnType>(url);
    return data;
  };

  const mutation = mutate(["useDeleteConversation"], deleteConversationFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetConversations"],
      });
    },
  });

  return mutation;
};
