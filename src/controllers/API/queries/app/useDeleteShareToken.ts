import { useMutationFunctionType } from "@/types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface DeleteShareTokenParams {
  app_id: string;
}

type DeleteShareTokenReturnType = string;

export const useDeleteShareToken: useMutationFunctionType<
  undefined,
  DeleteShareTokenParams,
  DeleteShareTokenReturnType
> = (options) => {
  const { mutate } = UseRequestProcessor();

  const deleteShareTokenFn = async (
    payload: DeleteShareTokenParams,
  ): Promise<DeleteShareTokenReturnType> => {
    const url = `${getURL("DELETE_SHARE_TOKEN", { app_id: payload.app_id })}/share`;
    const { data } = await api.delete<string>(url);
    return data;
  };

  const mutation = mutate(["useDeleteShareToken"], deleteShareTokenFn, {
    ...options,
  });

  return mutation;
};
