import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import { App } from "@/types/app";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetAppsParams {
  get_all?: boolean;
  page?: number;
  size?: number;
}

type GetAppsReturnType = App[];

export const useGetApps: useQueryFunctionType<
  GetAppsParams,
  GetAppsReturnType | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getAppsFn = async (
    params: GetAppsParams,
  ): Promise<GetAppsReturnType | undefined> => {
    const url = buildQueryStringUrl(`${getURL("GET_APPS")}`, params);
    const { data } = await api.get<GetAppsReturnType>(url);
    return data;
  };

  const queryResult = query(
    [
      "useGetApps",
      {
        get_all: params.get_all,
        page: params.page,
        size: params.size,
      },
    ],
    () => getAppsFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
