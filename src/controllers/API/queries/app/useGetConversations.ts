import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import { Conversation } from "@/types/app";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetConversationsParams {
  appId: string;
  get_all?: boolean;
  page?: number;
  size?: number;
}

type GetConversationsReturnType = Conversation[];

export const useGetConversations: useQueryFunctionType<
  GetConversationsParams,
  GetConversationsReturnType | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getAppsFn = async (
    params: GetConversationsParams,
  ): Promise<GetConversationsReturnType | undefined> => {
    const { appId, ...restParams } = params;
    const url = buildQueryStringUrl(
      `${getURL("GET_CONVERSATIONS", { appId })}/conversations`,
      restParams,
    );
    const { data } = await api.get<GetConversationsReturnType>(url);
    return data;
  };

  const queryResult = query(
    [
      "useGetConversations",
      {
        appId: params.appId,
        get_all: params.get_all,
        page: params.page,
        size: params.size,
      },
    ],
    () => getAppsFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
