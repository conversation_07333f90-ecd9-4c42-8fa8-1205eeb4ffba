import { useRef } from "react";

import type { AppConfig } from "@/types/app";
import { sse } from "../../api";
import { getURL } from "../../helpers/constants";

export type ActionType =
  | "prompt_message"
  | "prompt_message_end"
  | "statement_message"
  | "statement_message_end";

export interface MessageData {
  event: ActionType;
  content: string;
  conversation_id: string;
  created_at: string;
}

interface GeneratePromptParams {
  instruction: string;
  model: AppConfig["model"];
  no_opening_statement: boolean;
}

function parseJsonFromDataString(str) {
  try {
    const match = str.match(/^data:\s*(\{.*\})\n$/);
    if (!match) return null;

    const jsonStr = match[1];
    return JSON.parse(jsonStr);
  } catch (err) {
    console.error("JSON解析失败：", err);
    return null;
  }
}

export function useGeneratePrompt() {
  const abortController = useRef<AbortController | undefined>(undefined);

  function mutate(
    params: GeneratePromptParams,
    listeners: {
      onopen?: () => void;
      onmessage?: (data: MessageData) => void;
      onclose?: (data?: MessageData) => void;
    },
  ) {
    const url = getURL("PROMPT_GENERATE");
    abortController.current = new AbortController();
    try {
      sse(url, params, {
        signal: abortController.current.signal,
        openWhenHidden: true,
        async onopen() {
          listeners.onopen?.();
        },
        onmessage(ev) {
          const data: MessageData = parseJsonFromDataString(ev.data);
          if (!data) return;
          listeners.onmessage?.(data);
        },
        onclose() {
          listeners.onclose?.();
        },
      });
    } catch (error) {
      console.error(error);
    }
  }

  function abort() {
    abortController.current?.abort();
  }

  return { mutate, abort };
}
