import { useMutationFunctionType } from "@/types/api";
import type { App, UpdateAppParams } from "@/types/app";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

type UpdateAppReturnType = App;

export const useUpdateApp: useMutationFunctionType<
  undefined,
  UpdateAppParams,
  UpdateAppReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateAppFn = async (
    payload: UpdateAppParams,
  ): Promise<UpdateAppReturnType> => {
    const { app_id, ...rest } = payload;
    const url = getURL("UPDATE_APP", { app_id });
    const { data } = await api.patch<UpdateAppReturnType>(url, rest);

    return data;
  };

  const mutation = mutate(["useUpdateApp"], updateAppFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetApps"],
      });
    },
  });

  return mutation;
};
