import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { KnowledgeType } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetKnowledgesParams {
  keywords?: string;
  page?: number;
  page_size?: number;
}

type PaginatedKnowledgeType = {
  kbs: KnowledgeType[];
  total: number;
};

const addQueryParams = (url: string, params: GetKnowledgesParams): string => {
  return buildQueryStringUrl(url, params);
};

export const useGetKnowledges: useQueryFunctionType<
  GetKnowledgesParams,
  PaginatedKnowledgeType | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getKnowledgesFn = async (
    params: GetKnowledgesParams,
  ): Promise<PaginatedKnowledgeType | undefined> => {
    const url = addQueryParams(
      `${getURL("GET_KNOWLEDGES", {}, false, true)}`,
      params,
    );
    const { data: responseData } =
      await api.get<ResponseType<PaginatedKnowledgeType>>(url);
    return responseData.data;
  };

  const queryResult = query(
    [
      "useGetKnowledges",
      {
        keywords: params.keywords,
        page: params.page,
        page_size: params.page_size,
      },
    ],
    () => getKnowledgesFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
