import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { AddYuqueFileParams } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface AddYuqueFileResponse {
  message: string;
  book_id: string;
  total_selected: number;
  created_count: number;
  skipped_count: number;
  existing_rel_ids: string[];
}

export const useAddYuqueFile: useMutationFunctionType<
  undefined,
  AddYuqueFileParams,
  AddYuqueFileResponse
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const addYuqueFileFn = async (
    params: AddYuqueFileParams,
  ): Promise<AddYuqueFileResponse | undefined> => {
    const url = getURL("ADD_YUQUE_FILE", {}, false, true);
    const { data: responseData } = await api.post<
      ResponseType<AddYuqueFileResponse>
    >(url, params);
    return responseData.data;
  };

  const mutation = mutate(["useAddYuqueFile"], addYuqueFileFn, {
    retry: false,
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetDocuments"],
      });
    },
  });

  return mutation;
};
