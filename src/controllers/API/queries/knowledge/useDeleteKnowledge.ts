import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface DeleteKnowledgeParams {
  kb_id: string;
}

type DeleteKnowledgeReturnType = null;

export const useDeleteKnowledge: useMutationFunctionType<
  undefined,
  DeleteKnowledgeParams,
  DeleteKnowledgeReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteKnowledgeFn = async (
    payload: DeleteKnowledgeParams,
  ): Promise<DeleteKnowledgeReturnType> => {
    const url = getURL("DELETE_KNOWLEDGE", {}, false, true);
    const { data: responseData } = await api.post<
      ResponseType<DeleteKnowledgeReturnType>
    >(url, payload);

    return responseData.data;
  };

  const mutation = mutate(["useDeleteKnowledge"], deleteKnowledgeFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetKnowledges"],
      });
    },
  });

  return mutation;
};
