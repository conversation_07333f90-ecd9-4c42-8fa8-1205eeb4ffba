import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

type DeleteDocumentParams = string;

type DeleteDocumentReturnType = null;

export const useDeleteDocument: useMutationFunctionType<
  undefined,
  DeleteDocumentParams,
  DeleteDocumentReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteDocumentFn = async (
    payload: DeleteDocumentParams,
  ): Promise<DeleteDocumentReturnType> => {
    const url = getURL("DELETE_DOCUMENT", {}, false, true);
    const { data: responseData } = await api.post<
      ResponseType<DeleteDocumentReturnType>
    >(url, payload, {
      headers: {
        "Content-Type": "application/json",
      },
    });

    return responseData.data;
  };

  const mutation = mutate(["useDeleteDocument"], deleteDocumentFn, {
    retry: false,
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetDocuments"],
      });
    },
  });

  return mutation;
};
