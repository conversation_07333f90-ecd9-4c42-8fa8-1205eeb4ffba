import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { CreateKnowledgeParams } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

type CreateKnowledgeReturnType = {
  kb_id: string;
};

export const useCreateKnowledge: useMutationFunctionType<
  undefined,
  CreateKnowledgeParams,
  CreateKnowledgeReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const createKnowledgeFn = async (
    payload: CreateKnowledgeParams,
  ): Promise<CreateKnowledgeReturnType> => {
    const url = getURL("CREATE_KNOWLEDGE", {}, false, true);
    const { data: responseData } = await api.post<
      ResponseType<CreateKnowledgeReturnType>
    >(url, payload);

    return responseData.data;
  };

  const mutation = mutate(["useCreateKnowledge"], createKnowledgeFn, {
    ...options,
    onSettled: (response) => {
      if (response) {
        queryClient.refetchQueries({
          queryKey: ["useGetKnowledges"],
        });
      }
    },
  });

  return mutation;
};
