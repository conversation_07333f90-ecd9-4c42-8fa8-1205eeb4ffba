import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { YuqueDirectoryNodeItem } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetYuqueDirectoryNodesParams {
  yuque_token?: string;
  repo_id: string;
}

type DirectoryData = YuqueDirectoryNodeItem[];

const addQueryParams = (
  url: string,
  params: Partial<GetYuqueDirectoryNodesParams>,
): string => {
  return buildQueryStringUrl(url, params);
};

export const useGetYuqueDirectoryNodes: useMutationFunctionType<
  undefined,
  GetYuqueDirectoryNodesParams,
  DirectoryData
> = (options) => {
  const { mutate } = UseRequestProcessor();

  const getDirectoryFn = async (
    params: GetYuqueDirectoryNodesParams,
  ): Promise<DirectoryData | undefined> => {
    const { yuque_token, repo_id } = params;
    const url = addQueryParams(
      `${getURL("GET_YUQUE_DIRECTORY_NODES", { repo_id }, false, true)}/toc`,
      { yuque_token },
    );
    const { data: responseData } =
      await api.get<ResponseType<DirectoryData>>(url);
    return responseData.data;
  };

  const mutation = mutate(["useGetYuqueDirectoryNodes"], getDirectoryFn, {
    retry: false,
    ...options,
  });

  return mutation;
};
