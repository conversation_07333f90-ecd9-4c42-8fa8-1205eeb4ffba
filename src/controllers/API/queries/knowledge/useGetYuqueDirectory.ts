import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { KnowledgeType, YuqueDirectoryItem } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetYuqueDirectoryParams {
  user_id: string;
  yuque_token: string;
  type?: string;
  offset?: number;
  limit?: number;
}

type DirectoryData = YuqueDirectoryItem[];

const addQueryParams = (
  url: string,
  params: GetYuqueDirectoryParams,
): string => {
  return buildQueryStringUrl(url, params);
};

export const useGetYuqueDirectory: useMutationFunctionType<
  undefined,
  GetYuqueDirectoryParams,
  DirectoryData
> = (options) => {
  const { mutate } = UseRequestProcessor();

  const getDirectoryFn = async (
    params: GetYuqueDirectoryParams,
  ): Promise<DirectoryData | undefined> => {
    const url = addQueryParams(
      `${getURL("GET_YUQUE_DIRECTORY", {}, false, true)}`,
      params,
    );
    const { data: responseData } =
      await api.get<ResponseType<DirectoryData>>(url);
    return responseData.data;
  };

  const mutation = mutate(["useGetYuqueDirectory"], getDirectoryFn, {
    retry: false,
    ...options,
  });

  return mutation;
};
