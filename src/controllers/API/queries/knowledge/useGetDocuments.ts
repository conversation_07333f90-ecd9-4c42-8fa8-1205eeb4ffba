import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { PaginatedDocumentType } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface GetDocumentsParams {
  kb_id: string;
  keywords?: string;
  page?: number;
  page_size?: number;
  orderby?: string;
  desc?: boolean;
}

const addQueryParams = (url: string, params: GetDocumentsParams): string => {
  return buildQueryStringUrl(url, params);
};

export const useGetDocuments: useQueryFunctionType<
  GetDocumentsParams,
  PaginatedDocumentType | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getDocumentsFn = async (
    params: GetDocumentsParams,
  ): Promise<PaginatedDocumentType | undefined> => {
    const url = addQueryParams(
      `${getURL("GET_DOCUMENTS", {}, false, true)}`,
      params,
    );
    const { data: responseData } =
      await api.get<ResponseType<PaginatedDocumentType>>(url);
    return responseData.data;
  };

  const queryResult = query(
    [
      "useGetDocuments",
      {
        kb_id: params.kb_id,
        keywords: params.keywords,
        page: params.page,
        page_size: params.page_size,
        orderby: params.orderby,
        desc: params.desc,
      },
    ],
    () => getDocumentsFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
