import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface ChangeDocumentStatusParams {
  doc_id: string;
  status: 0 | 1;
}

type ChangeDocumentStatusReturnType = string;

const addQueryParams = (
  url: string,
  params: ChangeDocumentStatusParams,
): string => {
  return buildQueryStringUrl(url, params);
};

export const useChangeDocumentStatus: useMutationFunctionType<
  undefined,
  ChangeDocumentStatusParams,
  ChangeDocumentStatusReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const changeDocumentStatusFn = async (
    payload: ChangeDocumentStatusParams,
  ): Promise<ChangeDocumentStatusReturnType> => {
    const url = addQueryParams(
      getURL("CHANGE_DOCUMENT_STATUS", {}, false, true),
      {
        doc_id: payload.doc_id,
        status: payload.status,
      },
    );

    const { data: responseData } =
      await api.post<ResponseType<ChangeDocumentStatusReturnType>>(url);

    return responseData.data;
  };

  const mutation = mutate(["useChangeDocumentStatus"], changeDocumentStatusFn, {
    retry: false,
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetDocuments"],
      });
    },
  });

  return mutation;
};
