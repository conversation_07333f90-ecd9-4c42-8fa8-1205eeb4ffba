import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import { FileItem } from "@/types/knowledge";
import type { AxiosProgressEvent } from "axios";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface UploadDocumentParams {
  kb_id: string;
  file: FileItem;
}

type UploadDocumentReturnType = {
  id: string;
  kb_id: string;
  parser_id: string;
  parser_config: Record<string, any>;
  created_by: string;
  type: string;
  name: string;
  location: string;
  size: number;
  thumbnail: string;
};

type UploadDocumentProps = {
  onUploadProgress?: (
    fileId: string,
    progressEvent: AxiosProgressEvent,
  ) => void;
};

export const useUploadDocument: useMutationFunctionType<
  UploadDocumentProps,
  UploadDocumentParams,
  UploadDocumentReturnType[]
> = (props, options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const uploadDocumentFn = async (
    payload: UploadDocumentParams,
  ): Promise<UploadDocumentReturnType[]> => {
    const formData = new FormData();
    formData.append("kb_id", payload.kb_id);
    formData.append("files", payload.file.file);

    const url = getURL("UPLOAD_DOCUMENT", {}, false, true);
    const { data: responseData } = await api.post<
      ResponseType<UploadDocumentReturnType[]>
    >(url, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
      onUploadProgress: (progressEvent) => {
        props?.onUploadProgress?.(payload.file.id, progressEvent);
      },
    });

    return responseData.data;
  };

  const mutation = mutate(["useUploadDocument"], uploadDocumentFn, {
    ...options,
    onSettled: (response) => {
      if (response) {
        queryClient.refetchQueries({
          queryKey: ["useGetDocuments"],
        });
      }
    },
  });

  return mutation;
};
