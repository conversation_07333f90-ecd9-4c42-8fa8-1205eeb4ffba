import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RenameDocumentParams {
  doc_id: string;
  name: string;
}

type RenameDocumentReturnType = string;

const addQueryParams = (url: string, params: RenameDocumentParams): string => {
  return buildQueryStringUrl(url, params);
};

export const useRenameDocument: useMutationFunctionType<
  undefined,
  RenameDocumentParams,
  RenameDocumentReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const renameDocumentFn = async (
    payload: RenameDocumentParams,
  ): Promise<RenameDocumentReturnType> => {
    const url = addQueryParams(
      `${getURL("RENAME_DOCUMENT", {}, false, true)}`,
      payload,
    );

    const { data: responseData } =
      await api.post<ResponseType<RenameDocumentReturnType>>(url);

    return responseData.data;
  };

  const mutation = mutate(["useRenameDocument"], renameDocumentFn, {
    retry: false,
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetDocuments"],
      });
    },
  });

  return mutation;
};
