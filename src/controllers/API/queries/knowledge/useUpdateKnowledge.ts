import { useMutationFunctionType } from "@/types/api";
import type { ResponseType } from "@/types/common";
import type { CreateKnowledgeParams } from "@/types/knowledge";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface UpdateKnowledgeParams extends CreateKnowledgeParams {
  kb_id: string;
}

type UpdateKnowledgeReturnType = {
  id: string;
  name: string;
  avatar?: string;
  description?: string;
};

export const useUpdateKnowledge: useMutationFunctionType<
  undefined,
  UpdateKnowledgeParams,
  UpdateKnowledgeReturnType
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateKnowledgeFn = async (
    payload: UpdateKnowledgeParams,
  ): Promise<UpdateKnowledgeReturnType> => {
    const url = getURL("UPDATE_KNOWLEDGE", {}, false, true);
    const { data: responseData } = await api.post<
      ResponseType<UpdateKnowledgeReturnType>
    >(url, payload);

    return responseData.data;
  };

  const mutation = mutate(["useUpdateKnowledge"], updateKnowledgeFn, {
    ...options,
    onSettled: (response) => {
      if (response) {
        queryClient.refetchQueries({
          queryKey: ["useGetKnowledges"],
        });
      }
    },
  });

  return mutation;
};
