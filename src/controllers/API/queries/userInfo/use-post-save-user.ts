import { UseMutationResult } from "@tanstack/react-query";
import { useMutationFunctionType, Users } from "../../../../types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

export const usePostSaveUser: useMutationFunctionType<undefined, any> = (
  options?,
) => {
  const { mutate } = UseRequestProcessor();

  const saveUserInfo = async (params: Users) => {
    const response = await api.post<Users>(
      `${getURL("USER", {}, false, true)}/setting`,
      params,
    );
    return response["data"];
  };

  const mutation: UseMutationResult = mutate(
    ["usePostSaveUser"],
    saveUserInfo,
    options,
  );

  return mutation;
};
