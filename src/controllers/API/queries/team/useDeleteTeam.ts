import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id: string;
}

export const useDeleteTeam: useMutationFunctionType<
  undefined,
  RequestParams,
  string
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const deleteTeamFn = async (payload: RequestParams): Promise<string> => {
    const url = getURL(
      "DELETE_TEAM",
      { tenant_id: payload.tenant_id },
      false,
      true,
    );
    const res = await api.delete<ResponseType<string>>(url);
    return res.data?.data;
  };

  const mutation = mutate(["useDeleteTeam"], deleteTeamFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetTeamData"],
      });
    },
  });

  return mutation;
};
