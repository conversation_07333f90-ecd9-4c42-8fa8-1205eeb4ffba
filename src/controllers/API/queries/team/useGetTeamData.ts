import buildQueryStringUrl from "@/controllers/utils/create-query-param-string";
import { useQueryFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { TeamPageData } from "@/types/team";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  page?: number;
  size?: number;
  name?: string;
  status?: string;
  order_by?: string;
  desc?: boolean;
}

type ReturnData = TeamPageData;

export const useGetTeamData: useQueryFunctionType<
  RequestParams,
  ReturnData | undefined
> = (params, options) => {
  const { query } = UseRequestProcessor();

  const getTeamDataFn = async (
    params: RequestParams,
  ): Promise<ReturnData | undefined> => {
    const url = buildQueryStringUrl(
      `${getURL("GET_TEAM", {}, false, true)}`,
      params,
    );
    const res = await api.get<ResponseType<ReturnData>>(url);
    return res.data?.data;
  };

  const queryResult = query(
    [
      "useGetTeamData",
      {
        page: params.page,
        size: params.size,
        name: params.name,
        status: params.status,
      },
    ],
    () => getTeamDataFn(params),
    {
      refetchOnWindowFocus: false,
      ...options,
    },
  );

  return queryResult;
};
