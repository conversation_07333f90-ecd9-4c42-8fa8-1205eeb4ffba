import { useMutationFunctionType } from "@/types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  tenant_id: string;
}

interface ResponseParsm {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: string;
}

export const useSwitchTeam: useMutationFunctionType<
  undefined,
  RequestParams,
  ResponseParsm
> = (options) => {
  const { mutate } = UseRequestProcessor();

  const switchTeamFn = async (
    payload: RequestParams,
  ): Promise<ResponseParsm> => {
    const url = getURL("SWITCH_TEAM", {}, false, true);
    const res = await api.post(url, payload);
    return res.data?.data;
  };

  const mutation = mutate(["useSwitchTeam"], switchTeamFn, {
    ...options,
  });

  return mutation;
};
