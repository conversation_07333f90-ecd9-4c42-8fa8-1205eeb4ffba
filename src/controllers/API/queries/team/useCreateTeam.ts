import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { TeamData, TeamFormData } from "@/types/team";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams {
  name: string;
  llm_id: string;
  embd_id: string;
  asr_id: string;
  img2txt_id: string;
  rerank_id: string;
  tts_id: string;
  parser_ids: string;
  credit: number;
  public_key: string;
  expire_time: string;
  max_user_count: number;
}

export const useCreateTeam: useMutationFunctionType<
  undefined,
  TeamFormData,
  TeamData
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const createTeamFn = async (payload: RequestParams): Promise<TeamData> => {
    const url = getURL("CREATE_TEAM", {}, false, true);
    const data: RequestParams = {
      name: payload.name,
      max_user_count: payload.max_user_count,
      expire_time: payload.expire_time,
      // TODO: 对参数值进行确定或者不传递
      llm_id: "",
      embd_id: "",
      asr_id: "",
      img2txt_id: "",
      rerank_id: "",
      tts_id: "",
      parser_ids: "",
      credit: 0,
      public_key: "",
    };
    const res = await api.post<ResponseType<TeamData>>(url, data);
    return res.data?.data;
  };

  const mutation = mutate(["useCreateTeam"], createTeamFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetTeamData"],
      });
    },
  });

  return mutation;
};
