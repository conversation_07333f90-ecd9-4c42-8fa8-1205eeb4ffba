import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import { TeamData } from "@/types/team";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

type RequestParam = TeamData;

export const useUpdateTeam: useMutationFunctionType<
  undefined,
  RequestParam,
  TeamData
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateTeamFn = async (payload: RequestParam): Promise<TeamData> => {
    const { id, ...data } = payload;
    const url = getURL("UPDATE_TEAM", { id }, false, true);
    const res = await api.put<ResponseType<TeamData>>(url, data);
    return res.data?.data;
  };

  const mutation = mutate(["useUpdateTeam"], updateTeamFn, {
    ...options,
    onSettled: () => {
      queryClient.refetchQueries({
        queryKey: ["useGetTeamData"],
      });
    },
  });

  return mutation;
};
