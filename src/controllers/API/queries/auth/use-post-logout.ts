import useAuthStore from "@/stores/authStore";
import { useMutationFunctionType } from "@/types/api";

import { redirectToLogin } from "@/ragflow/utils/authorization-util";
import useFlowStore from "@/stores/flowStore";
import useFlowsManagerStore from "@/stores/flowsManagerStore";
import { useFolderStore } from "@/stores/foldersStore";
import { useNavigate } from "react-router-dom";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

export const useLogout: useMutationFunctionType<undefined, void> = (
  options?,
) => {
  const { mutate, queryClient } = UseRequestProcessor();
  const logout = useAuthStore((state) => state.logout);
  const navigate = useNavigate();

  async function logoutUser(): Promise<any> {
    const res = await api.post(`${getURL("LOGOUT", {}, false, true)}`);
    return res.data;
  }

  const mutation = mutate(["useLogout"], logoutUser, {
    onSuccess: () => {
      logout();

      useFlowStore.getState().resetFlowState();
      useFlowsManagerStore.getState().resetStore();
      useFolderStore.getState().resetStore();

      queryClient.invalidateQueries({ queryKey: ["useGetRefreshFlowsQuery"] });
      queryClient.invalidateQueries({ queryKey: ["useGetFolders"] });
      queryClient.invalidateQueries({ queryKey: ["useGetFolder"] });

      // 清空用户信息
      sessionStorage.clear();
      localStorage.clear();
      redirectToLogin();
    },
    onError: (error) => {
      console.error(error);
    },
    ...options,
  });

  return mutation;
};
