import { UseMutationResult } from "@tanstack/react-query";
import {
  APIV3WrapperType,
  useMutationFunctionType,
  Users,
} from "../../../../types/api";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

export const useGetUserData: useMutationFunctionType<undefined, any> = (
  options?,
) => {
  const { mutate } = UseRequestProcessor();

  const getUserData = async () => {
    const response = await api.get<APIV3WrapperType<Users>>(
      `${getURL("USER", {}, false, true)}/info`,
    );
    return response["data"]["data"];
  };

  const mutation: UseMutationResult = mutate(
    ["useGetUserData"],
    getUserData,
    options,
  );

  return mutation;
};
