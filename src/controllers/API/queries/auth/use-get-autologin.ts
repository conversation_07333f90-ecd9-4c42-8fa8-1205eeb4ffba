import { AuthContext } from "@/contexts/authContext";
import useAuthStore from "@/stores/authStore";
import { AxiosError } from "axios";
import { useContext } from "react";
import { useQueryFunctionType } from "../../../../types/api";
import { UseRequestProcessor } from "../../services/request-processor";

export interface AutoLoginResponse {
  frontend_timeout: number;
  auto_saving: boolean;
  auto_saving_interval: number;
  health_check_max_retries: number;
}

export const useGetAutoLogin: useQueryFunctionType<undefined, undefined> = (
  options,
) => {
  const { query } = UseRequestProcessor();
  const { login } = useContext(AuthContext);
  const setAutoLogin = useAuthStore((state) => state.setAutoLogin);

  async function getAutoLoginFn(): Promise<null> {
    try {
      const access_token = localStorage.getItem("access_token");
      // TODO: 原先的auto登录 改为 根据access_token登录
      if (access_token) {
        login(access_token, "auto");
        setAutoLogin(true);
      }
    } catch (e) {
      const error = e as AxiosError;
      if (error.name !== "CanceledError") {
        setAutoLogin(false);
      }
    }
    return null;
  }

  const queryResult = query(["useGetAutoLogin"], getAutoLoginFn, {
    refetchOnWindowFocus: false,
    ...options,
  });

  return queryResult;
};
