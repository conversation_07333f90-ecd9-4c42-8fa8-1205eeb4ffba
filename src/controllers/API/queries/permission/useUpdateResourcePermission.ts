import { useMutationFunctionType } from "@/types/api";
import { ResponseType } from "@/types/common";
import type { ResourcePermission } from "@/types/permission";
import { api } from "../../api";
import { getURL } from "../../helpers/constants";
import { UseRequestProcessor } from "../../services/request-processor";

interface RequestParams extends ResourcePermission {
  resource_type: "app" | "knowledgebase" | "workflow";
  resource_id: string;
}

type ReturnData = ResourcePermission;

export const useUpdateResourcePermission: useMutationFunctionType<
  undefined,
  RequestParams,
  ReturnData
> = (options) => {
  const { mutate, queryClient } = UseRequestProcessor();

  const updateResourcePermissionFn = async (
    payload: RequestParams,
  ): Promise<ReturnData> => {
    const { resource_id, resource_type, ...rest } = payload;
    const url = `${getURL("UPDATE_RESOURCE_PERMISSION", {}, false, true)}/${resource_type}/${resource_id}/permissions`;
    const res = await api.post<ResponseType<ReturnData>>(url, rest);
    return res.data?.data;
  };

  const mutation = mutate(
    ["useUpdateResourcePermission"],
    updateResourcePermissionFn,
    {
      ...options,
      onSettled: () => {
        queryClient.refetchQueries({
          queryKey: ["useGetResourcePermission"],
        });
      },
    },
  );

  return mutation;
};
