<svg width="160" height="160" viewBox="0 0 160 160" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Empty" clip-path="url(#clip0_2555_10338)">
<g id="Rectangle 22" filter="url(#filter0_d_2555_10338)">
<rect x="20.7998" y="25.6001" width="121.6" height="92.8" rx="9.6" fill="url(#paint0_linear_2555_10338)"/>
<rect x="21.4398" y="26.2401" width="120.32" height="91.52" rx="8.96" stroke="#3B72FF" stroke-width="1.28"/>
</g>
<rect id="Rectangle 23" x="27.2002" y="33.6001" width="24" height="76.8" rx="4.8" fill="url(#paint1_linear_2555_10338)"/>
<rect id="Rectangle 24" x="56" y="35.2" width="78.4" height="14.4" rx="4.8" fill="url(#paint2_linear_2555_10338)"/>
<rect id="Rectangle 25" x="57.5996" y="59.2" width="75.2" height="49.6" rx="6.4" stroke="#3B72FF" stroke-width="1.6" stroke-linecap="round" stroke-dasharray="6.4 6.4"/>
<path id="Vector 18" d="M35.2002 44.8H43.2002" stroke="#3B72FF" stroke-width="3.2" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 19" d="M35.2002 52.8H43.2002" stroke="#3B72FF" stroke-width="3.2" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 20" d="M35.2002 62.3999H43.2002" stroke="#3B72FF" stroke-width="3.2" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 21" d="M3.2002 91.814L6.10807 93.1497" stroke="#3B72FF" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 22" d="M7.5625 85.2378L9.83708 87.4886" stroke="#3B72FF" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
<path id="Vector 23" d="M15.1621 81.6797L16.068 84.7488" stroke="#3B72FF" stroke-width="1.6" stroke-linecap="round" stroke-linejoin="round"/>
<foreignObject x="1.45176" y="74.9833" width="70.4744" height="57.6802"><div xmlns="http://www.w3.org/1999/xhtml" style="backdrop-filter:blur(4.8px);clip-path:url(#bgblur_1_2555_10338_clip_path);height:100%;width:100%"></div></foreignObject><g id="Rectangle 26" data-figma-bg-blur-radius="9.6">
<path d="M11.9924 98.1622C10.9249 94.7926 12.7912 91.1957 16.1607 90.1282L22.6915 88.0592C24.22 87.575 25.8746 87.6821 27.3279 88.3594L31.3146 90.2174L34.6204 91.602C36.019 92.1878 37.5802 92.258 39.0257 91.8L51.3267 87.9031C53.7693 87.1293 56.3767 88.4821 57.1505 90.9247L61.3856 104.293C62.4531 107.663 60.5868 111.259 57.2173 112.327L26.2966 122.123C22.927 123.19 19.3301 121.324 18.2626 117.954L11.9924 98.1622Z" fill="url(#paint3_linear_2555_10338)" fill-opacity="0.5"/>
<path d="M31.3146 90.2174L27.3279 88.3594C25.8746 87.6821 24.22 87.575 22.6915 88.0592L16.1607 90.1282C12.7912 91.1957 10.9249 94.7926 11.9924 98.1622L18.2626 117.954C19.3301 121.324 22.927 123.19 26.2966 122.123L57.2173 112.327C60.5868 111.259 62.4531 107.663 61.3856 104.293L57.1505 90.9247C56.3767 88.4821 53.7693 87.1293 51.3267 87.9031V87.9031M31.3146 90.2174L34.6204 91.602C36.019 92.1878 37.5802 92.258 39.0257 91.8L51.3267 87.9031M31.3146 90.2174L46.5093 85.4037C48.5298 84.7636 50.6866 85.8826 51.3267 87.9031V87.9031" stroke="url(#paint4_linear_2555_10338)" stroke-width="1.28"/>
</g>
<g id="Ellipse 22" filter="url(#filter2_f_2555_10338)">
<ellipse cx="86.3998" cy="135.2" rx="65.6" ry="4" fill="url(#paint5_linear_2555_10338)" fill-opacity="0.1"/>
</g>
</g>
<defs>
<filter id="filter0_d_2555_10338" x="20.7998" y="25.6001" width="128" height="99.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="6.4" dy="6.4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.592157 0 0 0 0 0.713726 0 0 0 0 1 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2555_10338"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2555_10338" result="shape"/>
</filter>
<clipPath id="bgblur_1_2555_10338_clip_path" transform="translate(-1.45176 -74.9833)"><path d="M11.9924 98.1622C10.9249 94.7926 12.7912 91.1957 16.1607 90.1282L22.6915 88.0592C24.22 87.575 25.8746 87.6821 27.3279 88.3594L31.3146 90.2174L34.6204 91.602C36.019 92.1878 37.5802 92.258 39.0257 91.8L51.3267 87.9031C53.7693 87.1293 56.3767 88.4821 57.1505 90.9247L61.3856 104.293C62.4531 107.663 60.5868 111.259 57.2173 112.327L26.2966 122.123C22.927 123.19 19.3301 121.324 18.2626 117.954L11.9924 98.1622Z"/>
</clipPath><filter id="filter2_f_2555_10338" x="11.1998" y="121.6" width="150.4" height="27.2" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4.8" result="effect1_foregroundBlur_2555_10338"/>
</filter>
<linearGradient id="paint0_linear_2555_10338" x1="81.5998" y1="118.4" x2="81.5998" y2="25.6001" gradientUnits="userSpaceOnUse">
<stop stop-color="#F5FCFF"/>
<stop offset="1" stop-color="#ECF3FF"/>
</linearGradient>
<linearGradient id="paint1_linear_2555_10338" x1="39.2002" y1="33.6001" x2="39.2002" y2="110.4" gradientUnits="userSpaceOnUse">
<stop stop-color="#8BB4FF"/>
<stop offset="1" stop-color="#8BA4FF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint2_linear_2555_10338" x1="134.4" y1="42.4" x2="56" y2="42.4" gradientUnits="userSpaceOnUse">
<stop stop-color="#8BA4FF" stop-opacity="0.5"/>
<stop offset="1" stop-color="#8BB4FF"/>
</linearGradient>
<linearGradient id="paint3_linear_2555_10338" x1="41.7569" y1="117.225" x2="31.6211" y2="85.2303" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#ECF3FF"/>
</linearGradient>
<linearGradient id="paint4_linear_2555_10338" x1="19.1016" y1="94.5539" x2="47.6336" y2="116.527" gradientUnits="userSpaceOnUse">
<stop stop-color="#3B72FF"/>
<stop offset="1" stop-color="#7CB0FF"/>
</linearGradient>
<linearGradient id="paint5_linear_2555_10338" x1="152" y1="135.2" x2="20.7998" y2="135.2" gradientUnits="userSpaceOnUse">
<stop stop-color="#8BA4FF"/>
<stop offset="1" stop-color="#8BB4FF"/>
</linearGradient>
<clipPath id="clip0_2555_10338">
<rect width="160" height="160" fill="white"/>
</clipPath>
</defs>
</svg>
