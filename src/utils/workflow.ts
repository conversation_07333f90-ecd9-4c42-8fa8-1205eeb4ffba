import { NODE_CATEGORIES, NODE_SELECT_ITEMS } from "@/constants/workflow";
import { NodeCategoryConfig, NodeSelectItem } from "@/types/workflow/node";

/**
 * 根据关键词过滤节点
 */
export const filterNodesByKeyword = (keyword: string): NodeSelectItem[] => {
  if (!keyword.trim()) {
    return NODE_SELECT_ITEMS;
  }

  const lowerKeyword = keyword.toLowerCase().trim();
  return NODE_SELECT_ITEMS.filter(
    (item) =>
      item.label.toLowerCase().includes(lowerKeyword) ||
      item.description.toLowerCase().includes(lowerKeyword),
  );
};

/**
 * 根据过滤后的节点重新组织分类
 */
export const getCategoriesWithFilteredNodes = (
  filteredNodes: NodeSelectItem[],
): NodeCategoryConfig[] => {
  return NODE_CATEGORIES.map((category) => ({
    ...category,
    nodes: filteredNodes.filter((item) => item.category === category.key),
  })).filter((category) => category.nodes.length > 0);
};
