import JSEncrypt from "jsencrypt";

// RSA加密公钥
const PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
 MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEArq9XTUSeYr2+N1h3Afl/
 z8Dse/2yD0ZGrKwx+EEEcdsBLca9Ynmx3nIB5obmLlSfmskLpBo0UACBmB5rEjBp
 2Q2f3AG3Hjd4B+gNCG6BDaawuDlgANIhGnaTLrIqWrrcm4EMzJOnAOI1fgzJRsOO
 UEfaS318Eq9OVO3apEyCCt0lOQK6PuksduOjVxtltDav+guVAA068NrPYmRNabVK
 RNLJpL8w4D44sfth5RvZ3q9t+6RTArpEtc5sh5ChzvqPOzKGMXW83C95TxmXqpbK
 6olN4RevSfVjEAgCydH6HN6OhtOQEcnrU97r9H0iZOWwbw3pVrZiUkuRD1R56Wzs
 2wIDAQAB
 -----END PUBLIC KEY-----`;

// 加密密码函数
export const rsaEncryptPassword = (password: string) => {
  // 先转义所有特殊字符（如 UTF-8 编码）
  const encodedStr = encodeURIComponent(password);
  // 先进行Base64编码
  const base64Password = btoa(encodedStr);

  // 使用RSA加密
  const encryptor = new JSEncrypt();
  encryptor.setPublicKey(PUBLIC_KEY);

  return encryptor.encrypt(base64Password) || password;
};

// 解密密码函数
export const rsaDecryptPassword = (encryptedPassword: string) => {
  // 使用RSA解密
  const decryptor = new JSEncrypt();
  decryptor.setPrivateKey(PUBLIC_KEY);

  return decryptor.decrypt(encryptedPassword);
};
